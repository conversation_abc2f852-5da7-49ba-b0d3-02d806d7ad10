/**
 * 日志用户筛选扩展
 * 
 * 这个脚本增强了原有的filterLogsByUser函数，
 * 解决特殊用户名（如"用户+数字类型的用户"）日志筛选问题
 */

// 创建防抖函数，避免短时间内多次执行
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// 防止重复处理的标志
let isProcessingLogs = false;
let currentProcessingUsername = null; // 记录当前正在处理的用户名

// 添加最近处理记录，防止短时间内重复处理同一用户
const recentlyProcessed = new Map(); // 用户名 -> 处理时间戳
const PROCESS_COOLDOWN = 2000; // 同一用户2秒内不重复处理

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('日志用户筛选扩展已加载');
    
    // 保存原始函数的引用
    const originalFilterLogsByUser = window.filterLogsByUser;
    
    // 如果原始函数存在，替换它
    if (typeof originalFilterLogsByUser === 'function') {
        // 重新定义函数，增强用户日志筛选功能
        window.filterLogsByUser = debounce(function(username) {
            // 保存当前处理的用户名 - 在函数开始时固定下来，避免后续变化
            const originalUsername = username;
            
            // 检查是否为最近处理过的用户，如果是且时间间隔太短则跳过
            const now = Date.now();
            if (recentlyProcessed.has(originalUsername)) {
                const lastProcessTime = recentlyProcessed.get(originalUsername);
                if (now - lastProcessTime < PROCESS_COOLDOWN) {
                    console.log(`用户 ${originalUsername} 最近已处理过，跳过此次请求(冷却中: ${Math.round((now - lastProcessTime) / 100) / 10}秒)`);
                    return;
                }
            }
            
            // 更新该用户的最近处理时间
            recentlyProcessed.set(originalUsername, now);
            
            // 如果正在处理中且是相同的用户，跳过
            if (isProcessingLogs && currentProcessingUsername === originalUsername) {
                console.log(`正在处理用户日志，跳过重复请求: ${originalUsername}`);
                return;
            }
            
            // 如果正在处理其他用户，等待处理完成
            if (isProcessingLogs) {
                console.log(`正在处理其他用户日志，延迟处理: ${originalUsername}`);
                setTimeout(() => window.filterLogsByUser(originalUsername), 200);
                return;
            }
            
            // 标记为开始处理，保存当前处理的用户名
            isProcessingLogs = true;
            currentProcessingUsername = originalUsername;
            
            console.log(`增强版筛选函数处理用户: ${originalUsername}`);
            
            const logTableBody = document.querySelector('.log-table tbody');
            if (!logTableBody) {
                isProcessingLogs = false;
                currentProcessingUsername = null;
                return;
            }
            
            // 如果当前已经在显示该用户的日志，则不重复加载
            let runningLogBtn = document.getElementById('runningLogBtn');
            if (window.currentFilteredUsername === originalUsername &&
                runningLogBtn &&
                runningLogBtn.innerText.trim() === `用户【${originalUsername}】的日志`) {
                console.log(`已经在显示用户 ${originalUsername} 的日志，不重复加载`);
                isProcessingLogs = false;
                currentProcessingUsername = null;
                return;
            }
            
            // 保存当前筛选的用户名
            window.currentFilteredUsername = originalUsername;
            
            // 重置查看所有日志的标记
            window.viewingAllLogs = false;
            
            // 清空现有日志
            logTableBody.innerHTML = '';
            
            // 创建加载指示器
            const loadingRow = document.createElement('tr');
            const loadingCell = document.createElement('td');
            loadingCell.colSpan = 3;
            loadingCell.textContent = `正在加载用户 ${originalUsername} 的日志...`;
            loadingCell.style.textAlign = 'center';
            loadingCell.style.padding = '20px';
            loadingRow.appendChild(loadingCell);
            logTableBody.appendChild(loadingRow);
            
            // 获取当前实例ID
            const instanceId = window.currentLogInstanceId || '1'; // 默认使用实例1
            
            // 更新按钮文本，先显示加载状态
            runningLogBtn = document.getElementById('runningLogBtn');
            if (runningLogBtn) {
                runningLogBtn.setAttribute('data-processing-username', originalUsername); // 在按钮上记录当前处理的用户名
                runningLogBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i>正在加载 ${originalUsername} 的日志...`;
                // 清除特殊标记
                delete runningLogBtn.dataset.viewMode;
            }
            
            // 创建更灵活的匹配模式
            let userPatterns = [
                new RegExp(`【${originalUsername}】`),
                // 如果用户名本身包含【】，也尝试匹配其中的内容
                new RegExp(`${originalUsername.replace(/[【】]/g, '')}`)
            ];
            
            // 对于"用户+数字"格式，添加更宽松的匹配
            if (/^用户\d+$/.test(originalUsername)) {
                userPatterns.push(new RegExp(originalUsername));
            }
            
            console.log(`应用 ${userPatterns.length} 种匹配模式查找用户 ${originalUsername} 的日志`);

            // 预处理用户名，移除【】等符号
            const cleanUsername = originalUsername.replace(/[【】\[\]]/g, '');
            console.log(`处理后的用户名: ${cleanUsername}`);
            
            // 使用闭包来保存当前处理上下文
            const processContext = {
                originalUsername: originalUsername,
                cleanUsername: cleanUsername,
                userPatterns: userPatterns,
                runningLogBtn: runningLogBtn,
                logTableBody: logTableBody
            };
            
            // 从本地存储加载用户日志文件
            if (window.electronAPI && typeof window.electronAPI.loadUserLogFile === 'function') {
                // 先移除所有之前的监听器，避免重复处理
                window.electronAPI.removeAllListeners('load-user-log-file-reply');
                
                // 创建唯一的请求ID，用于跟踪处理完成
                const requestId = Date.now().toString() + Math.random().toString(36).substr(2, 5);
                processContext.requestId = requestId;
                
                // 使用处理后的用户名
                window.electronAPI.loadUserLogFile(instanceId, cleanUsername);
                
                // 添加一次性监听器，接收加载结果
                const handleReply = (result) => {
                    // 验证当前处理的用户名是否仍然匹配
                    if (currentProcessingUsername !== processContext.originalUsername) {
                        console.warn(`用户上下文已变更，当前:${currentProcessingUsername}，期望:${processContext.originalUsername}，忽略结果`);
                        return;
                    }
                    
                    if (result.success && result.data && result.data.pollingId) {
                        console.log(`成功加载用户 ${processContext.cleanUsername} 的日志文件，轮询ID: ${result.data.pollingId}`);
                        
                        // 加载轮询日志
                        loadPollingLogs(instanceId, result.data.pollingId, processContext);
                    } else {
                        console.warn(`加载用户 ${processContext.cleanUsername} 的日志文件失败，使用备用方法筛选日志`);
                        useFallbackFilter(processContext);
                    }
                };
                
                // 添加一次性监听器
                window.electronAPI.once('load-user-log-file-reply', handleReply);
            } else {
                console.warn(`electronAPI不可用，使用备用方法筛选日志`);
                useFallbackFilter(processContext);
            }
            
            // 加载轮询日志文件
            function loadPollingLogs(instanceId, pollingId, context) {
                if (window.electronAPI && typeof window.electronAPI.loadPollingLogFile === 'function') {
                    // 先移除所有之前的监听器，避免重复处理
                    window.electronAPI.removeAllListeners('load-polling-log-file-reply');
                    
                    window.electronAPI.loadPollingLogFile(instanceId, pollingId);
                    
                    // 添加一次性监听器，接收加载结果
                    const handleReply = (result) => {
                        // 验证当前处理的用户名是否仍然匹配
                        if (currentProcessingUsername !== context.originalUsername) {
                            console.warn(`用户上下文已变更，当前:${currentProcessingUsername}，期望:${context.originalUsername}，忽略结果`);
                            return;
                        }
                        
                        // 清空加载指示器
                        context.logTableBody.innerHTML = '';
                        
                        if (result.success && Array.isArray(result.logs) && result.logs.length > 0) {
                            // 显示轮询的所有日志，按时间倒序排列
                            const allLogs = [...result.logs].reverse();
                            
                            console.log(`显示用户 ${context.originalUsername} 的轮询日志，共 ${allLogs.length} 条`);
                            
                            // 添加所有日志
                            allLogs.forEach(log => {
                                const logRow = createLogTableRow(log);
                                context.logTableBody.appendChild(logRow);
                            });
                            
                            // 更新按钮文本
                            if (context.runningLogBtn) {
                                // 验证按钮上的用户名是否匹配
                                const btnUsername = context.runningLogBtn.getAttribute('data-processing-username');
                                if (btnUsername === context.originalUsername) {
                                    // 检查用户名是否已经包含【】，避免重复添加
                                    const displayUsername = context.originalUsername.includes('【') ?
                                        context.originalUsername :
                                        `【${context.originalUsername}】`;

                                    // 获取实例ID：优先使用当前实例ID，否则从用户所属实例获取，最后默认为1
                                    let instanceId = window.currentLogInstanceId;
                                    if (!instanceId) {
                                        // 如果没有当前实例ID，尝试从监控用户列表中找到该用户所属的实例
                                        for (const [id, users] of Object.entries(window.monitoredUsers || {})) {
                                            if (users && users.includes(context.originalUsername)) {
                                                instanceId = id;
                                                break;
                                            }
                                        }
                                        // 如果还是没找到，默认为实例1
                                        if (!instanceId) {
                                            instanceId = '1';
                                        }
                                    }

                                    context.runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户${displayUsername}的日志`;
                                } else {
                                    console.warn(`按钮用户名不匹配，当前:${btnUsername}，期望:${context.originalUsername}，不更新按钮`);
                                }
                            }
                            
                            // 处理完成
                            isProcessingLogs = false;
                            currentProcessingUsername = null;
                        } else {
                            console.warn(`加载轮询 ${pollingId} 的日志文件失败，使用备用方法筛选日志`);
                            useFallbackFilter(context);
                        }
                    };
                    
                    // 添加一次性监听器
                    window.electronAPI.once('load-polling-log-file-reply', handleReply);
                } else {
                    console.warn(`electronAPI不可用，使用备用方法筛选日志`);
                    useFallbackFilter(context);
                }
            }
            
            // 备用筛选方法
            function useFallbackFilter(context) {
                console.log(`使用备用方法过滤用户 ${context.originalUsername} 的日志`);
                
                // 过滤包含该用户名的日志
                const filteredLogs = window.logs.filter(log => {
                    if (!log.message && !log.content) return false;
                    
                    // 尝试所有匹配模式
                    for (const pattern of context.userPatterns) {
                        if ((log.message && pattern.test(log.message)) || 
                            (log.content && pattern.test(log.content))) {
                            return true;
                        }
                    }
                    return false;
                }).reverse(); // 按时间倒序排列
                
                console.log(`找到 ${filteredLogs.length} 条与用户 ${context.originalUsername} 匹配的日志`);
                
                // 清空加载指示器
                context.logTableBody.innerHTML = '';
                
                if (filteredLogs.length > 0) {
                    // 添加过滤后的日志
                    filteredLogs.forEach(log => {
                        const logRow = window.createLogTableRow(log);
                        context.logTableBody.appendChild(logRow);
                    });
                    
                    // 更新按钮文本
                    if (context.runningLogBtn) {
                        // 验证按钮上的用户名是否匹配
                        const btnUsername = context.runningLogBtn.getAttribute('data-processing-username');
                        if (btnUsername === context.originalUsername) {
                            // 检查用户名是否已经包含【】，避免重复添加
                            const displayUsername = context.originalUsername.includes('【') ?
                                context.originalUsername :
                                `【${context.originalUsername}】`;

                            // 获取实例ID：优先使用当前实例ID，否则从用户所属实例获取，最后默认为1
                            let instanceId = window.currentLogInstanceId;
                            if (!instanceId) {
                                // 如果没有当前实例ID，尝试从监控用户列表中找到该用户所属的实例
                                for (const [id, users] of Object.entries(window.monitoredUsers || {})) {
                                    if (users && users.includes(context.originalUsername)) {
                                        instanceId = id;
                                        break;
                                    }
                                }
                                // 如果还是没找到，默认为实例1
                                if (!instanceId) {
                                    instanceId = '1';
                                }
                            }

                            context.runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户${displayUsername}的日志`;
                        } else {
                            console.warn(`按钮用户名不匹配，当前:${btnUsername}，期望:${context.originalUsername}，不更新按钮`);
                        }
                    }
                } else {
                    // 显示无日志提示
                    const noLogRow = document.createElement('tr');
                    const noLogCell = document.createElement('td');
                    noLogCell.colSpan = 3;
                    noLogCell.textContent = `没有找到用户 ${context.originalUsername} 的日志`;
                    noLogCell.style.textAlign = 'center';
                    noLogCell.style.padding = '20px';
                    noLogRow.appendChild(noLogCell);
                    context.logTableBody.appendChild(noLogRow);
                    
                    // 更新按钮文本
                    if (context.runningLogBtn) {
                        // 验证按钮上的用户名是否匹配
                        const btnUsername = context.runningLogBtn.getAttribute('data-processing-username');
                        if (btnUsername === context.originalUsername) {
                            // 检查用户名是否已经包含【】，避免重复添加
                            const displayUsername = context.originalUsername.includes('【') ?
                                context.originalUsername :
                                `【${context.originalUsername}】`;

                            // 获取实例ID：优先使用当前实例ID，否则从用户所属实例获取，最后默认为1
                            let instanceId = window.currentLogInstanceId;
                            if (!instanceId) {
                                // 如果没有当前实例ID，尝试从监控用户列表中找到该用户所属的实例
                                for (const [id, users] of Object.entries(window.monitoredUsers || {})) {
                                    if (users && users.includes(context.originalUsername)) {
                                        instanceId = id;
                                        break;
                                    }
                                }
                                // 如果还是没找到，默认为实例1
                                if (!instanceId) {
                                    instanceId = '1';
                                }
                            }

                            context.runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户${displayUsername}的日志`;
                        } else {
                            console.warn(`按钮用户名不匹配，当前:${btnUsername}，期望:${context.originalUsername}，不更新按钮`);
                        }
                    }
                }
                
                // 处理完成
                isProcessingLogs = false;
                currentProcessingUsername = null;
            }
        }, 100); // 100ms防抖延迟
        
        console.log('日志用户筛选函数已替换');
    }
}); 