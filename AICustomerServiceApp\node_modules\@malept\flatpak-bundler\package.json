{"name": "@malept/flatpak-bundler", "version": "0.4.0", "description": "A small utility for packing files in a flatpak.", "main": "index.js", "repository": {"type": "git", "url": "git+https://github.com/malept/flatpak-bundler.git"}, "keywords": ["flatpak"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/malept/flatpak-bundler/issues"}, "files": [], "homepage": "https://github.com/malept/flatpak-bundler#readme", "engines": {"node": ">= 10.0.0"}, "dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "devDependencies": {"husky": "^4.2.5", "lint-staged": "^10.2.9", "mocha": "^7.0.0", "standard": "^14.0.0"}, "scripts": {"lint": "standard", "mocha": "mocha", "test": "standard && mocha"}, "husky": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": "standard --fix"}}