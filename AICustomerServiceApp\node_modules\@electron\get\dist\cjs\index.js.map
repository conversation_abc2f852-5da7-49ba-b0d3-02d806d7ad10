{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;AAAA,iCAA0B;AAC1B,+BAA+B;AAC/B,6BAA6B;AAC7B,iCAAiC;AACjC,yCAAyC;AAEzC,qDAAiG;AAOjG,mCAAgC;AAChC,+DAA+D;AAC/D,mCAA0C;AAC1C,mCAOiB;AAEjB,iCAAsC;AAA7B,8BAAA,WAAW,CAAA;AACpB,iCAA0C;AAAjC,kCAAA,eAAe,CAAA;AAGxB,MAAM,CAAC,GAAG,eAAK,CAAC,qBAAqB,CAAC,CAAC;AAEvC,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;IACtC,uBAAe,EAAE,CAAC;CACnB;AAMD,KAAK,UAAU,gBAAgB,CAC7B,eAAwC,EACxC,mBAA2B,EAC3B,iBAAqC;IAErC,OAAO,MAAM,2BAAmB,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,EAAC,UAAU,EAAC,EAAE;QACjF,uDAAuD;QACvD,0DAA0D;QAC1D,IACE,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC;YACtD,CAAC,eAAe,CAAC,wBAAwB;YACzC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,EAC5C;YACA,IAAI,UAAkB,CAAC;YACvB,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;YAC5C,IAAI,SAAS,EAAE;gBACb,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBACxD,MAAM,SAAS,GAAa,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC1B,MAAM,IAAI,KAAK,CACb,8EAA8E,CAC/E,CAAC;iBACH;gBACD,MAAM,kBAAkB,GAAG,SAAS;qBACjC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;qBACtD,IAAI,CAAC,IAAI,CAAC,CAAC;gBACd,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;aACpD;iBAAM;gBACL,UAAU,GAAG,MAAM,iBAAiB,CAAC;oBACnC,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,eAAe,CAAC,OAAO;oBAChC,YAAY,EAAE,gBAAgB;oBAC9B,KAAK,EAAE,eAAe,CAAC,KAAK;oBAC5B,eAAe,EAAE,eAAe,CAAC,eAAe;oBAChD,SAAS,EAAE,eAAe,CAAC,SAAS;oBACpC,UAAU,EAAE,eAAe,CAAC,UAAU;oBACtC,aAAa,EAAE,eAAe,CAAC,aAAa;iBAC7C,CAAC,CAAC;aACJ;YAED,kFAAkF;YAClF,sEAAsE;YACtE,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE;gBAC9D,MAAM,gBAAgB,GAA+B,EAAE,CAAC;gBACxD,gBAAgB,CAAC,mBAAmB,GAAG,QAAQ,CAAC;gBAChD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBACzF,MAAM,OAAO,CAAC,QAAQ,CACpB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EACjC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CACnC,CAAC;aACH;iBAAM;gBACL,MAAM,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,EAAE;oBACxE,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAC;iBACnC,CAAC,CAAC;aACJ;SACF;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACI,KAAK,UAAU,gBAAgB,CACpC,gBAA6D;IAE7D,MAAM,eAAe,qBACf,gBAA4C,CACjD,CAAC;IACF,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE;QAC/B,MAAM,uBAAuB,GAAG,eAAkD,CAAC;QACnF,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;YACrC,CAAC,CAAC,oDAAoD,CAAC,CAAC;YACxD,uBAAuB,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACrD;QACD,IAAI,uBAAuB,CAAC,IAAI,EAAE;YAChC,uBAAuB,CAAC,IAAI,GAAG,mBAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;SAC1E;aAAM;YACL,CAAC,CAAC,4CAA4C,CAAC,CAAC;YAChD,uBAAuB,CAAC,IAAI,GAAG,mBAAW,EAAE,CAAC;SAC9C;KACF;IACD,4BAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IAEjD,eAAe,CAAC,OAAO,GAAG,mCAAkB,CAAC,eAAe,CAAC,CAAC;IAC9D,MAAM,QAAQ,GAAG,oCAAmB,CAAC,eAAe,CAAC,CAAC;IACtD,MAAM,GAAG,GAAG,MAAM,qCAAoB,CAAC,eAAe,CAAC,CAAC;IACxD,MAAM,KAAK,GAAG,IAAI,aAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAEnD,mEAAmE;IACnE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;QAC1B,CAAC,CAAC,uBAAuB,eAAe,CAAC,SAAS,SAAS,QAAQ,KAAK,GAAG,GAAG,CAAC,CAAC;QAChF,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEpE,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,CAAC,CAAC,YAAY,CAAC,CAAC;SACjB;aAAM;YACL,CAAC,CAAC,WAAW,CAAC,CAAC;YACf,IAAI;gBACF,MAAM,gBAAgB,CAAC,eAAe,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBAEtE,OAAO,UAAU,CAAC;aACnB;YAAC,OAAO,GAAG,EAAE;gBACZ,CAAC,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC;gBACnD,CAAC,CAAC,6BAA6B,CAAC,CAAC;aAClC;SACF;KACF;IAED,IACE,CAAC,eAAe,CAAC,SAAS;QAC1B,mCAA2B,CACzB,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,IAAI,EACpB,eAAe,CAAC,OAAO,EACvB,eAAe,CAAC,aAAa,CAC9B,EACD;QACA,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC3D,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;KAChF;IAED,OAAO,MAAM,2BAAmB,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,EAAC,UAAU,EAAC,EAAE;QACjF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,oCAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;QAExF,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,IAAI,CAAC,MAAM,4CAAsB,EAAE,CAAC,CAAC;QAClF,CAAC,CACC,eAAe,GAAG,OAAO,gBAAgB,kBAAkB,IAAI,CAAC,SAAS,CACvE,eAAe,CAAC,eAAe,CAChC,EAAE,CACJ,CAAC;QACF,MAAM,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,gBAAgB,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC;QAElF,MAAM,gBAAgB,CAAC,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;QAE5E,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC;AA1ED,4CA0EC;AAED;;;;;GAKG;AACH,SAAgB,QAAQ,CACtB,OAAe,EACf,OAAwC;IAExC,OAAO,gBAAgB,iCAClB,OAAO,KACV,OAAO,EACP,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAC1B,IAAI,EAAE,OAAO,CAAC,IAAI,EAClB,YAAY,EAAE,UAAU,IACxB,CAAC;AACL,CAAC;AAXD,4BAWC"}