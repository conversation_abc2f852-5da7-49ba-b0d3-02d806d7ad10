﻿        window.selectedApp = null;
        window.instanceCounter = 1;
        // 存储每个实例的设置数据
        window.instanceSettings = {};
        // 存储每个应用的设置数据
        window.appSettings = {};

        // 防抖函数定义
        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
        }

        // 在DOMContentLoaded事件中加载应用和实例设置
        document.addEventListener('DOMContentLoaded', function() {
            // 监听浏览器窗口关闭事件
            if (window.electronAPI && typeof window.electronAPI.onInstanceBrowserClosed === 'function') {
                // 创建一个安全的事件处理函数，捕获所有可能的错误
                const safeHandleBrowserClosed = (data) => {
                    try {
                        // 检查数据是否有效
                        if (!data || typeof data !== 'object' || !data.instanceId) {
                            console.warn('收到无效的浏览器窗口关闭事件数据:', data);
                            return;
                        }

                        const { instanceId } = data;
                        console.log(`收到实例 ${instanceId} 的浏览器窗口关闭事件`);

                        // 检查实例设置是否存在
                        if (!window.instanceSettings || !window.instanceSettings[instanceId]) {
                            console.warn(`实例 ${instanceId} 的设置不存在，无法更新激活状态`);
                            return;
                        }

                        // 更新实例的激活状态
                        window.instanceSettings[instanceId].activated = false;
                        console.log(`实例 ${instanceId} 的激活状态已更新为关闭`);

                        // 保存到本地存储
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            try {
                                categoryStorageAdapter.saveInstance(instanceId, window.instanceSettings[instanceId]);
                                console.log(`实例 ${instanceId} 的激活状态已保存到本地存储`);
                            } catch (storageError) {
                                console.error(`保存实例 ${instanceId} 的激活状态时出错:`, storageError);
                            }
                        }

                        // 安全地更新UI
                        try {
                            const instanceItems = document.querySelectorAll('.instance-item');
                            if (instanceItems && instanceItems.length > 0) {
                                let updated = false;
                                instanceItems.forEach(item => {
                                    try {
                                        const instanceIdElement = item.querySelector('.instance-id');
                                        if (!instanceIdElement) return;

                                        const itemId = instanceIdElement.textContent.replace('任务ID: ', '');
                                        if (itemId === instanceId.toString()) {
                                            const toggleBtn = item.querySelector('.toggle-btn');
                                            if (toggleBtn) {
                                                toggleBtn.classList.remove('active');
                                                console.log(`已更新实例 ${instanceId} 的UI开关状态为关闭`);
                                                updated = true;
                                            }
                                        }
                                    } catch (itemError) {
                                        console.warn(`处理实例项时出错:`, itemError);
                                    }
                                });

                                if (!updated) {
                                    console.warn(`未找到实例 ${instanceId} 的UI元素，无法更新开关状态`);
                                }
                            } else {
                                console.warn(`未找到实例列表元素，无法更新UI状态`);
                            }
                        } catch (uiError) {
                            console.error(`更新实例 ${instanceId} 的UI状态时出错:`, uiError);
                        }
                    } catch (error) {
                        console.error('处理浏览器窗口关闭事件时出错:', error);
                    }
                };

                // 注册事件处理函数
                window.electronAPI.onInstanceBrowserClosed(safeHandleBrowserClosed);
                console.log('已注册实例浏览器窗口关闭事件监听器');
            }
            // 从本地存储加载应用设置
            if (typeof categoryStorageAdapter !== 'undefined') {
                console.log('开始从本地存储加载应用设置...');
                categoryStorageAdapter.loadAllApps(function(result) {
                    if (result.success && result.apps) {
                        console.log('从本地存储加载应用设置:', result.apps);
                        window.appSettings = result.apps;
                    } else {
                        console.warn('从本地存储加载应用设置失败或数据为空');
                    }
                });
            } else {
                console.warn('未找到categoryStorageAdapter，无法加载应用设置');
            }

            // 从本地存储加载实例设置
            if (typeof categoryStorageAdapter !== 'undefined') {
                console.log('开始从本地存储加载实例设置...');
                categoryStorageAdapter.loadAllInstances(function(result) {
                    if (result.success && result.instances) {
                        console.log('从本地存储加载实例设置:', result.instances);
                        window.instanceSettings = result.instances;

                        // 实例列表将在后面的代码中恢复，这里不需要调用
                        console.log('已加载实例设置，稍后将恢复实例列表');
                    } else {
                        console.warn('从本地存储加载实例设置失败或数据为空');
                    }
                });
            } else {
                console.warn('未找到categoryStorageAdapter，无法加载实例设置');
            }
        });
        // 初始化关键词数据对象
        window.keywordData = {};

        // 确保 categoryStorageAdapter 已经初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从本地存储加载关键词数据
            if (typeof categoryStorageAdapter !== 'undefined') {
                console.log('开始从本地存储加载关键词数据...');
                categoryStorageAdapter.loadAllKeywords(function(result) {
                if (result.success && result.keywords) {
                    console.log('从本地存储加载关键词数据成功:', result.keywords);

                    // 将关键词数据转换为适合前端使用的格式
                    if (result.keywords.groups && Object.keys(result.keywords.groups).length > 0) {
                        // 加载分类组
                        window.keywordCategories = Object.values(result.keywords.groups);
                        console.log('已加载关键词分类组:', window.keywordCategories.length);
                    }

                    if (result.keywords.replies && Object.keys(result.keywords.replies).length > 0) {
                        // 加载关键词回复
                        Object.entries(result.keywords.replies).forEach(([groupId, replies]) => {
                            // 初始化分类组的关键词数组
                            window.keywordData[groupId] = [];

                            // 将关键词回复添加到数组中
                            Object.values(replies).forEach(reply => {
                                window.keywordData[groupId].push(reply);
                            });

                            console.log(`已加载分类 ${groupId} 的关键词回复:`, window.keywordData[groupId].length);
                        });
                    }

                    // 如果关键词页面已经初始化，则更新UI
                    if (window.keywordPageInitialized && typeof initCategoryList === 'function') {
                        initCategoryList();
                        console.log('已更新关键词分类列表');

                        // 刷新关键词列表
                        if (typeof window.renderKeywordList === 'function') {
                            window.renderKeywordList();
                            console.log('已刷新关键词列表');
                        }

                        // 更新关键词总数和列表
                        if (typeof updateKeywordCountAndList === 'function') {
                            updateKeywordCountAndList();
                            console.log('已更新关键词总数和列表');
                        }
                    } else {
                        console.log('关键词页面尚未初始化，数据已加载到内存中，待页面初始化后使用');
                    }
                } else {
                    console.warn('从本地存储加载关键词数据失败或数据为空');
                }
            });
            } else {
                console.warn('未找到categoryStorageAdapter，无法加载关键词数据');
            }
        });
        // 全局设置
        window.globalSettings = {
            keywordMatching: true,
            aiAutoReply: true,
            keywordReplacement: true,
            pauseOnHotkey: true
        };

        // 在DOMContentLoaded事件中加载全局设置
        document.addEventListener('DOMContentLoaded', function() {
            // 从本地存储加载全局设置
            if (typeof categoryStorageAdapter !== 'undefined') {
                console.log('开始从本地存储加载全局设置...');
                categoryStorageAdapter.loadGlobalSettings(function(result) {
                    if (result.success && result.settings) {
                        console.log('从本地存储加载全局设置:', result.settings);
                        window.globalSettings = result.settings;

                        // 更新底部控制栏开关状态
                        if (typeof updateToggleSwitches === 'function') {
                            updateToggleSwitches();
                            console.log('已更新底部控制栏开关状态');
                        }
                    } else {
                        console.warn('从本地存储加载全局设置失败或数据为空');
                    }
                });
            } else {
                console.warn('未找到categoryStorageAdapter，无法加载全局设置');
            }
        });

        // 更新底部控制栏开关状态
        function updateToggleSwitches() {
            document.getElementById('keywordMatchingToggle').checked = window.globalSettings.keywordMatching;
            document.getElementById('aiAutoReplyToggle').checked = window.globalSettings.aiAutoReply;
            document.getElementById('keywordReplacementToggle').checked = window.globalSettings.keywordReplacement;
            document.getElementById('pauseOnHotkeyToggle').checked = window.globalSettings.pauseOnHotkey;
        }
        // 特殊设置
        window.specialSettings = {
            sendTriggerWords: false
        };
        // 日志数据
        window.logs = [];

        // 监控到的用户数据
        window.monitoredUsers = {};

        // 当前选中的实例ID（用于日志过滤）
        window.currentLogInstanceId = null;

        // 在页面加载完成后初始化数据
        document.addEventListener('DOMContentLoaded', function() {
            // 等待存储适配器初始化完成
            if (typeof categoryStorageAdapter !== 'undefined') {
                categoryStorageAdapter.waitForInit(() => {
                    // 加载应用设置
                    const loadedAppSettings = categoryStorageAdapter.loadAllApps();
                    if (loadedAppSettings && Object.keys(loadedAppSettings).length > 0) {
                        appSettings = loadedAppSettings;
                        console.log('从本地存储加载应用设置:', appSettings);
                    }

                    // 加载实例设置
                    const loadedInstanceSettings = categoryStorageAdapter.loadAllInstances();
                    if (loadedInstanceSettings && Object.keys(loadedInstanceSettings).length > 0) {
                        instanceSettings = loadedInstanceSettings;
                        console.log('从本地存储加载实例设置:', instanceSettings);

                        // 更新实例计数器
                        const instanceIds = Object.keys(instanceSettings).map(id => parseInt(id)).filter(id => !isNaN(id));
                        if (instanceIds.length > 0) {
                            instanceCounter = Math.max(...instanceIds) + 1;
                        }

                        // 恢复实例列表
                        restoreInstanceList();
                    }

                    // 加载关键词数据
                    const loadedKeywordData = categoryStorageAdapter.loadAllKeywords();
                    if (loadedKeywordData) {
                        keywordData = loadedKeywordData;
                        console.log('从本地存储加载关键词数据:', keywordData);

                        // TODO: 恢复关键词列表
                        // restoreKeywordList();
                    }

                    // 加载全局设置
                    const loadedGlobalSettings = categoryStorageAdapter.loadGlobalSettings();
                    if (loadedGlobalSettings) {
                        globalSettings = loadedGlobalSettings;
                        console.log('从本地存储加载全局设置:', globalSettings);

                        // 更新UI上的全局开关状态
                        updateGlobalSettingsUI();
                    }

                    // 加载特殊设置
                    const loadedSpecialSettings = categoryStorageAdapter.loadSpecialSettings();
                    if (loadedSpecialSettings) {
                        window.specialSettings = loadedSpecialSettings;
                        console.log('从本地存储加载特殊设置:', window.specialSettings);

                        // 更新UI上的特殊设置
                        updateSpecialSettingsUI();
                    }

                    // 加载日志
                    const loadedLogs = categoryStorageAdapter.loadLogs();
                    if (loadedLogs && loadedLogs.length > 0) {
                        window.logs = loadedLogs;
                        console.log('从本地存储加载日志:', window.logs);

                        // 更新日志显示
                        updateLogsDisplay();
                    }
                });

                // 监听新的监控日志
                if (window.electronAPI && typeof window.electronAPI.onNewMonitorLog === 'function') {
                    window.electronAPI.onNewMonitorLog((logEntry) => {
                        console.log('收到新的监控日志:', logEntry);

                        // 添加到日志数组
                        window.logs.push(logEntry);

                        // 不限制日志数量，保留所有历史日志
                        // 注释掉原有的限制代码
                        // if (window.logs.length > 1000) {
                        //     window.logs = window.logs.slice(-1000);
                        // }

                        // 更新日志显示
                        updateLogsDisplay(logEntry);
                    });
                }

                // 监听清空监控日志事件
                if (window.electronAPI && typeof window.electronAPI.onClearMonitorLogs === 'function') {
                    window.electronAPI.onClearMonitorLogs(() => {
                        console.log('收到清空监控日志事件');

                        // 清空日志表格
                        const logTableBody = document.querySelector('.log-table tbody');
                        if (logTableBody) {
                            logTableBody.innerHTML = '';
                        }

                        // 清空已显示日志的记录
                        if (window.displayedLogIds) {
                            window.displayedLogIds.clear();
                            console.log('已清空日志显示记录');
                        }
                    });
                }

                // 监听监控用户列表更新
                if (window.electronAPI && typeof window.electronAPI.onUpdateMonitoredUsers === 'function') {
                    window.electronAPI.onUpdateMonitoredUsers((data) => {
                        const { instanceId, users } = data;
                        console.log(`收到实例 ${instanceId} 的监控用户列表更新:`, users);

                        // 更新用户列表 - 直接使用接收到的用户列表，不进行任何排序
                        window.monitoredUsers[instanceId] = [...users]; // 创建一个新数组，避免引用原数组

                        // 输出用户列表，用于调试
                        console.log(`实例 ${instanceId} 的用户列表(更新后):`, window.monitoredUsers[instanceId]);

                        // 更新用户列表显示
                        updateMonitoredUsersDisplay();
                    });
                }

                // 初始化日志右键菜单
                initLogContextMenu();

                // 加载所有实例的监控用户列表
                if (window.electronAPI && typeof window.electronAPI.getAllMonitoredUsers === 'function') {
                    window.electronAPI.getAllMonitoredUsers();
                    window.electronAPI.onGetAllMonitoredUsersReply((result) => {
                        if (result.success) {
                            // 直接使用接收到的用户列表，不进行任何排序
                            window.monitoredUsers = {};

                            // 为每个实例创建一个新的用户数组，避免引用原数组
                            Object.keys(result.allUsers || {}).forEach(instanceId => {
                                if (Array.isArray(result.allUsers[instanceId])) {
                                    // 确保用户列表按照监控顺序排序，不进行任何基于用户名的排序
                                    window.monitoredUsers[instanceId] = [...result.allUsers[instanceId]];

                                    // 输出用户列表，用于调试
                                    console.log(`实例 ${instanceId} 的用户列表:`, window.monitoredUsers[instanceId]);
                                }
                            });

                            console.log('加载所有实例的监控用户列表:', window.monitoredUsers);
                            updateMonitoredUsersDisplay();
                        } else {
                            console.error('加载所有实例的监控用户列表失败:', result.error);
                        }
                    });
                }

                // 添加导出数据按钮（如果不存在）
                const footerLeft = document.querySelector('.footer-left');

                // 添加导入数据按钮（如果不存在）
                if (!footerLeft.querySelector('.footer-icon[title="导入数据"]')) {
                    const importBtn = document.createElement('div');
                    importBtn.className = 'footer-icon';
                    importBtn.innerHTML = '<i class="fas fa-upload"></i>';
                    importBtn.title = '导入数据';
                    importBtn.style.marginLeft = '10px';
                    importBtn.addEventListener('click', function() {
                        categoryStorageAdapter.importData();
                    });
                    footerLeft.appendChild(importBtn);
                    console.log('添加导入数据按钮');
                }
                
                // 日志区用户排序选择器
                const logUserSortSelector = document.getElementById('logUserSortMethod');
                if (logUserSortSelector) {
                    // 加载保存的排序方法
                    if (window.electronAPI && typeof window.electronAPI.getSetting === 'function') {
                        window.electronAPI.getSetting('logUserSortMethod').then(method => {
                            if (method) {
                                logUserSortSelector.value = method;
                            }
                        }).catch(err => {
                            console.error('加载日志用户排序方法失败:', err);
                        });
                    }
                    
                    // 监听排序方法变更
                    logUserSortSelector.addEventListener('change', function() {
                        const method = this.value;
                        console.log('日志用户排序方法已更改为:', method);
                        
                        // 保存选择的排序方法
                        if (window.electronAPI && typeof window.electronAPI.updateSetting === 'function') {
                            window.electronAPI.updateSetting('logUserSortMethod', method);
                            window.electronAPI.onUpdateSettingReply((result) => {
                                if (result.success) {
                                    console.log('日志用户排序方法已保存');
                                } else {
                                    console.error('保存日志用户排序方法失败:', result.error);
                                }
                            });
                        }
                        
                        // 重新显示用户列表
                        updateMonitoredUsersDisplay();
                    });
                }
            }
        });

        // 更新全局设置 UI
        function updateGlobalSettingsUI() {
            // 更新底部控制栏的开关状态
            const keywordMatchingToggle = document.querySelector('#keywordMatchingToggle');
            if (keywordMatchingToggle) {
                keywordMatchingToggle.checked = globalSettings.keywordMatching;
            }

            const aiAutoReplyToggle = document.querySelector('#aiAutoReplyToggle');
            if (aiAutoReplyToggle) {
                aiAutoReplyToggle.checked = globalSettings.aiAutoReply;
            }

            const keywordReplacementToggle = document.querySelector('#keywordReplacementToggle');
            if (keywordReplacementToggle) {
                keywordReplacementToggle.checked = globalSettings.keywordReplacement;
            }

            const pauseOnHotkeyToggle = document.querySelector('#pauseOnHotkeyToggle');
            if (pauseOnHotkeyToggle) {
                pauseOnHotkeyToggle.checked = globalSettings.pauseOnHotkey;
            }
        }

        // 更新特殊设置 UI
        function updateSpecialSettingsUI() {
            // 更新特殊触发词复选框状态
            const specialTriggerCheck = document.getElementById('specialTriggerCheck');
            if (specialTriggerCheck && window.specialSettings) {
                specialTriggerCheck.checked = window.specialSettings.sendTriggerWords === true;
                console.log('更新特殊触发词复选框状态:', specialTriggerCheck.checked);
            }
        }

        // 初始化应用设置数据
        function initAppSettings(appId) {
            // 创建新的应用设置对象，无需激活字段
            window.appSettings[appId] = {
                general: {
                    defaultReply: "当前消息有点多，我稍后再回复您【or】您好，有什么能帮助...",
                    transferReply: "已为您转接其他客服"
                },
                ai: {
                    mode: "other",
                    apiAddress: "http://*************:8080/api",
                    appId: "38a09dda-eab9-11ef-96b6-0242ac120007",
                    apiKey: "application-d887bc7431c6d04cca4ad067a72bc21c"
                }
            };
            console.log(`初始化应用 ${appId} 的设置`, JSON.parse(JSON.stringify(window.appSettings[appId])));

            // 保存到本地存储
            if (typeof categoryStorageAdapter !== 'undefined') {
                categoryStorageAdapter.saveApp(appId, window.appSettings[appId]);
                console.log(`应用 ${appId} 的初始设置已保存到本地存储`);
            }
        }

        // 加载应用设置到界面
        function loadAppSettings(appId) {
            console.log(`开始加载应用 ${appId} 的设置`);

            // 确保为当前应用ID创建独立的设置对象
            if (!window.appSettings[appId]) {
                console.log(`应用 ${appId} 尚未初始化，正在创建默认设置`);
                initAppSettings(appId);
            }

            // 使用深拷贝获取当前应用的设置，避免引用问题
            const settings = JSON.parse(JSON.stringify(window.appSettings[appId]));
            console.log(`成功获取应用 ${appId} 的设置:`, settings);

            // 获取应用名称
            let appName = "应用";
            if (appId === "douyin") {
                appName = "抖店";
            }

            // 更新设置模态框标题
            document.querySelector('.settings-title').textContent = `${appName} 设置`;

            // 隐藏激活/取消激活按钮（应用设置不需要激活功能）
            document.querySelector('.settings-deactivate').style.display = 'none';

            // 更新通用设置
            document.querySelectorAll('#generalSettings .settings-input')[0].value = settings.general.defaultReply;
            document.querySelectorAll('#generalSettings .settings-input')[1].value = settings.general.transferReply;

            // 更新AI设置
            const aiInputs = document.querySelectorAll('#aiSettings .settings-input');
            aiInputs[0].value = settings.ai.apiAddress;
            aiInputs[1].value = settings.ai.appId;
            aiInputs[2].value = settings.ai.apiKey;

            // 更新单选按钮
            document.querySelectorAll('input[name="aiMode"]').forEach(radio => {
                radio.checked = radio.value === settings.ai.mode;
                radio.dataset.originalChecked = radio.checked ? "true" : "false";
            });

            // 保存原始值
            aiInputs.forEach(input => {
                input.dataset.originalValue = input.value;
            });

            // 将当前应用ID保存到设置模态框的数据属性中
            document.getElementById('settingsModal').dataset.currentAppId = appId;
            document.getElementById('settingsModal').dataset.currentInstanceId = '';
            document.getElementById('settingsModal').dataset.settingType = 'app';

            console.log(`应用 ${appId} 的设置已加载到界面`);
        }

        // 保存应用设置
        function saveAppSettings(appId) {
            if (!appId || !window.appSettings[appId]) return;

            // 使用当前应用的设置作为基础，避免覆盖或混淆其他设置
            const currentSettings = Object.assign({}, window.appSettings[appId]);

            // 保存通用设置
            currentSettings.general = {
                defaultReply: document.querySelectorAll('#generalSettings .settings-input')[0].value,
                transferReply: document.querySelectorAll('#generalSettings .settings-input')[1].value
            };

            // 保存AI设置
            const aiInputs = document.querySelectorAll('#aiSettings .settings-input');
            currentSettings.ai = {
                mode: document.querySelector('input[name="aiMode"]:checked').value,
                apiAddress: aiInputs[0].value,
                appId: aiInputs[1].value,
                apiKey: aiInputs[2].value
            };

            // 更新设置
            window.appSettings[appId] = currentSettings;
            console.log(`应用 ${appId} 的设置已保存:`, JSON.parse(JSON.stringify(window.appSettings[appId])));

            // 保存到本地存储
            if (typeof categoryStorageAdapter !== 'undefined') {
                categoryStorageAdapter.saveApp(appId, currentSettings);
                console.log(`应用 ${appId} 的设置已保存到本地存储`);
            }
        }

        // 初始化实例设置数据
        function initInstanceSettings(instanceId, fromAppId = null) {
            // 获取当前时间
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            // 如果提供了应用ID，则从应用设置复制数据作为模板
            if (fromAppId && window.appSettings[fromAppId]) {
                console.log(`从应用 ${fromAppId} 复制设置作为实例 ${instanceId} 的初始值`);

                // 使用深拷贝确保数据独立
                const appSettingsCopy = JSON.parse(JSON.stringify(window.appSettings[fromAppId]));

                // 创建实例的设置对象，加入激活字段并复制应用设置
                window.instanceSettings[instanceId] = {
                    activated: false,
                    createTime: timeStr, // 保存创建时间
                    general: appSettingsCopy.general,
                    ai: appSettingsCopy.ai
                };
            } else {
                // 没有模板应用，使用默认值
                window.instanceSettings[instanceId] = {
                    activated: false,
                    createTime: timeStr, // 保存创建时间
                    general: {
                        defaultReply: "当前消息有点多，我稍后再回复您【or】您好，有什么能帮助...",
                        transferReply: "已为您转接其他客服"
                    },
                    ai: {
                        mode: "other",
                        apiAddress: "http://*************:8080/api",
                        appId: "38a09dda-eab9-11ef-96b6-0242ac120007",
                        apiKey: "application-d887bc7431c6d04cca4ad067a72bc21c"
                    }
                };
            }

            console.log(`初始化实例 ${instanceId} 的设置`, JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));

            // 保存到本地存储
            if (typeof categoryStorageAdapter !== 'undefined') {
                categoryStorageAdapter.saveInstance(instanceId, window.instanceSettings[instanceId]);
                console.log(`实例 ${instanceId} 的初始设置已保存到本地存储`);
            }
        }

        // 加载实例设置到界面
        function loadInstanceSettings(instanceId) {
            console.log(`开始加载实例 ${instanceId} 的设置`);

            // 确保为当前实例ID创建独立的设置对象
            if (!window.instanceSettings[instanceId]) {
                console.log(`实例 ${instanceId} 尚未初始化，正在创建默认设置`);
                initInstanceSettings(instanceId);
            }

            // 使用深拷贝获取当前实例的设置，避免引用问题
            const settings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));
            console.log(`成功获取实例 ${instanceId} 的设置:`, settings);

            // 更新激活状态
            document.querySelector('.settings-title').textContent = `任务ID ${instanceId} 设置`;
            const deactivateBtn = document.querySelector('.settings-deactivate');

            if (settings.activated) {
                deactivateBtn.classList.remove('not-activated');
                deactivateBtn.classList.add('activated');
            } else {
                deactivateBtn.classList.add('not-activated');
                deactivateBtn.classList.remove('activated');
            }

            // 更新通用设置
            document.querySelectorAll('#generalSettings .settings-input')[0].value = settings.general.defaultReply;
            document.querySelectorAll('#generalSettings .settings-input')[1].value = settings.general.transferReply;

            // 更新AI设置
            const aiInputs = document.querySelectorAll('#aiSettings .settings-input');
            aiInputs[0].value = settings.ai.apiAddress;
            aiInputs[1].value = settings.ai.appId;
            aiInputs[2].value = settings.ai.apiKey;

            // 更新单选按钮
            document.querySelectorAll('input[name="aiMode"]').forEach(radio => {
                radio.checked = radio.value === settings.ai.mode;
                radio.dataset.originalChecked = radio.checked ? "true" : "false";
            });

            // 保存原始值
            aiInputs.forEach(input => {
                input.dataset.originalValue = input.value;
            });

            // 将当前实例ID保存到设置模态框的数据属性中
            document.getElementById('settingsModal').dataset.currentInstanceId = instanceId;

            console.log(`实例 ${instanceId} 的设置已加载到界面`);
        }

        // 保存实例设置
        function saveInstanceSettings(instanceId) {
            if (!instanceId || !window.instanceSettings[instanceId]) return;

            // 使用当前实例的设置作为基础，避免覆盖或混淆其他设置
            const currentSettings = Object.assign({}, window.instanceSettings[instanceId]);

            // 保存激活状态
            currentSettings.activated = document.querySelector('.settings-deactivate').classList.contains('activated');

            // 保存通用设置
            currentSettings.general = {
                defaultReply: document.querySelectorAll('#generalSettings .settings-input')[0].value,
                transferReply: document.querySelectorAll('#generalSettings .settings-input')[1].value
            };

            // 保存AI设置
            const aiInputs = document.querySelectorAll('#aiSettings .settings-input');
            currentSettings.ai = {
                mode: document.querySelector('input[name="aiMode"]:checked').value,
                apiAddress: aiInputs[0].value,
                appId: aiInputs[1].value,
                apiKey: aiInputs[2].value
            };

            // 更新设置
            window.instanceSettings[instanceId] = currentSettings;
            console.log(`实例 ${instanceId} 的设置已保存:`, JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));

            // 保存到本地存储
            if (typeof categoryStorageAdapter !== 'undefined') {
                categoryStorageAdapter.saveInstance(instanceId, currentSettings);
                console.log(`实例 ${instanceId} 的设置已保存到本地存储`);
            }
        }

        // 应用选择功能
        document.querySelectorAll('.app-item').forEach(app => {
            app.addEventListener('click', function(e) {
                // 如果点击的是图标按钮，则不处理选中状态
                if (e.target.closest('.icon-btn')) {
                    return;
                }

                // 切换当前应用的选中状态
                this.classList.toggle('selected');

                // 更新选中的应用和创建按钮状态
                if (this.classList.contains('selected')) {
                    selectedApp = this.getAttribute('data-app-id');
                    document.getElementById('createInstanceBtn').classList.remove('disabled');
                } else {
                    selectedApp = null;
                    document.getElementById('createInstanceBtn').classList.add('disabled');
                }
            });
        });

        // 应用中的图标按钮点击事件
        document.querySelectorAll('.app-item .icon-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡到app-item
                const actionType = this.getAttribute('data-tooltip');
                const appItem = this.closest('.app-item');
                const appId = appItem.getAttribute('data-app-id');

                if (actionType === '设置') {
                    // 重置界面状态
                    document.getElementById('settingsModal').dataset.currentInstanceId = '';
                    document.getElementById('settingsModal').dataset.currentAppId = '';
                    document.getElementById('settingsModal').dataset.settingType = '';

                    // 重置菜单状态 - 始终从通用设置开始显示
                    document.querySelectorAll('.settings-menu-item').forEach(item => {
                        item.classList.remove('active');
                        if (item.textContent === '通用设置') {
                            item.classList.add('active');
                        }
                    });

                    // 重置内容区域显示
                    document.getElementById('generalSettings').style.display = 'block';
                    document.getElementById('aiSettings').style.display = 'none';

                    // 初始化设置模态窗中的数据为默认值
                    document.querySelectorAll('#generalSettings .settings-input')[0].value = '';
                    document.querySelectorAll('#generalSettings .settings-input')[1].value = '';
                    document.querySelectorAll('#aiSettings .settings-input').forEach(input => {
                        input.value = '';
                        input.dataset.originalValue = '';
                    });

                    // 载入应用设置
                    console.log('准备加载应用设置:', appId);
                    loadAppSettings(appId);

                    // 直接显示设置弹窗（无需激活步骤）
                    document.getElementById('settingsModal').classList.add('show');
                }

                if (actionType === '帮助') {
                    document.getElementById('helpModal').classList.add('show');
                    document.getElementById('helpOverlay').classList.add('show');
                }
            });
        });

        // 自定义提示框函数
        function showCustomAlert(message, showCancel = false) {
            return new Promise((resolve) => {
                const alertEl = document.getElementById('customAlert');
                const overlayEl = document.getElementById('helpOverlay');
                const messageEl = document.getElementById('alertMessage');
                const cancelBtn = document.getElementById('alertCancel');
                const confirmBtn = document.getElementById('alertConfirm');

                messageEl.textContent = message;
                cancelBtn.style.display = showCancel ? 'block' : 'none';

                alertEl.classList.add('show');
                overlayEl.classList.add('show');

                const hideAlert = () => {
                    alertEl.classList.remove('show');
                    overlayEl.classList.remove('show');
                };

                confirmBtn.onclick = () => {
                    hideAlert();
                    resolve(true);
                };

                cancelBtn.onclick = () => {
                    hideAlert();
                    resolve(false);
                };

                overlayEl.onclick = () => {
                    hideAlert();
                    resolve(false);
                };
            });
        }

        // 自定义确认框函数 - 支持回调风格
        function showCustomConfirm(message, callback) {
            // 获取DOM元素
            const alertEl = document.getElementById('customAlert');
            const overlayEl = document.getElementById('helpOverlay');
            const messageEl = document.getElementById('alertMessage');
            const cancelBtn = document.getElementById('alertCancel');
            const confirmBtn = document.getElementById('alertConfirm');

            // 设置消息和显示取消按钮
            messageEl.textContent = message;
            cancelBtn.style.display = 'block'; // 确认框总是显示取消按钮

            // 显示对话框
            alertEl.classList.add('show');
            overlayEl.classList.add('show');

            // 关闭对话框的函数
            const hideAlert = () => {
                alertEl.classList.remove('show');
                overlayEl.classList.remove('show');
            };

            // 处理确认按钮点击
            const onConfirm = () => {
                hideAlert();
                // 移除事件监听器，避免内存泄漏
                confirmBtn.removeEventListener('click', onConfirm);
                cancelBtn.removeEventListener('click', onCancel);
                overlayEl.removeEventListener('click', onCancel);
                // 回调函数，传递true表示确认
                if (typeof callback === 'function') callback(true);
            };

            // 处理取消按钮点击
            const onCancel = () => {
                hideAlert();
                // 移除事件监听器，避免内存泄漏
                confirmBtn.removeEventListener('click', onConfirm);
                cancelBtn.removeEventListener('click', onCancel);
                overlayEl.removeEventListener('click', onCancel);
                // 回调函数，传递false表示取消
                if (typeof callback === 'function') callback(false);
            };

            // 添加事件监听器
            confirmBtn.addEventListener('click', onConfirm);
            cancelBtn.addEventListener('click', onCancel);
            overlayEl.addEventListener('click', onCancel);
        }

        // 修改实例操作代码，确保正确加载实例设置
        function bindInstanceEvents(newInstance) {
            const toggleBtn = newInstance.querySelector('.toggle-btn');
            const settingsBtn = newInstance.querySelector('.icon-btn[data-tooltip="设置"]');
            const deleteBtn = newInstance.querySelector('.icon-btn[data-tooltip="删除"]');
            const instanceId = newInstance.querySelector('.instance-id').textContent.split(': ')[1];

            console.log('绑定实例事件，按钮元素:', {
                toggleBtn: toggleBtn,
                settingsBtn: settingsBtn,
                deleteBtn: deleteBtn,
                instanceId: instanceId
            });

            // 绑定实例操作按钮事件
            toggleBtn.addEventListener('click', function() {
                this.classList.toggle('active');
                const isActive = this.classList.contains('active');
                console.log(`实例 ${instanceId} 开关状态: ${isActive ? '开启' : '关闭'}`);

                // 更新实例设置
                if (window.instanceSettings[instanceId]) {
                    window.instanceSettings[instanceId].activated = isActive;
                    console.log(`实例 ${instanceId} 激活状态已更新为: ${window.instanceSettings[instanceId].activated}`);

                    // 保存到本地存储
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        categoryStorageAdapter.saveInstance(instanceId, window.instanceSettings[instanceId]);
                        console.log(`实例 ${instanceId} 的激活状态已保存到本地存储`);
                    }

                    // 如果实例被激活，并且是抖店应用，打开浏览器
                    if (isActive) {
                        // 获取实例对应的应用ID
                        const appId = window.instanceSettings[instanceId].appId || 'douyin'; // 默认为抖店

                        if (appId === 'douyin') {
                            // 打开抖店客服网址
                            if (window.electronAPI && typeof window.electronAPI.openInstanceBrowser === 'function' &&
                                typeof window.electronAPI.getInstanceBrowserStatus === 'function') {

                                // 首先检查浏览器窗口是否已经打开
                                window.electronAPI.getInstanceBrowserStatus(instanceId);

                                // 创建一个一次性的事件处理函数，仅处理当前实例ID的响应
                                const handleBrowserStatusReply = browserStatus => {
                                    // 确保只处理当前实例的响应
                                    if (browserStatus.instanceId !== instanceId) {
                                        console.log(`忽略非当前实例的浏览器状态响应: ${browserStatus.instanceId}`);
                                        return;
                                    }

                                    // 处理完成后移除监听器，避免影响其他实例
                                    window.electronAPI.removeListener('get-instance-browser-status-reply', handleBrowserStatusReply);

                                    if (browserStatus.success && !browserStatus.isOpen) {
                                        // 如果浏览器未打开，则打开它
                                        const url = 'https://im.jinritemai.com/pc_seller_v2/main/workspace';
                                        console.log(`打开实例 ${instanceId} 的内部浏览器，访问抖店客服网址: ${url}`);
                                        window.electronAPI.openInstanceBrowser(instanceId, url);

                                        // 注册回调函数来处理浏览器打开结果
                                        const handleOpenBrowserReply = result => {
                                            // 确保只处理当前实例的响应
                                            if (result.instanceId !== instanceId) return;

                                            // 处理完成后移除监听器
                                            window.electronAPI.removeListener('open-instance-browser-reply', handleOpenBrowserReply);

                                            if (result.success) {
                                                console.log(`实例 ${instanceId} 的内部浏览器打开成功`);
                                            } else {
                                                console.error(`实例 ${instanceId} 的内部浏览器打开失败:`, result.error);
                                                // 如果内部浏览器打开失败，尝试使用外部浏览器
                                                if (typeof window.electronAPI.openBrowser === 'function') {
                                                    console.log(`尝试使用外部浏览器打开 URL: ${url}`);
                                                    window.electronAPI.openBrowser(url);
                                                }
                                            }
                                        };

                                        window.electronAPI.onOpenInstanceBrowserReply(handleOpenBrowserReply);
                                    } else if (browserStatus.success && browserStatus.isOpen) {
                                        console.log(`实例 ${instanceId} 的内部浏览器已经打开，不需要重复打开`);
                                    } else {
                                        console.error(`无法获取实例 ${instanceId} 的浏览器状态:`, browserStatus.error);
                                    }
                                };

                                window.electronAPI.onGetInstanceBrowserStatusReply(handleBrowserStatusReply);
                            }
                        }
                    } else {
                        // 如果实例被关闭，关闭对应的浏览器
                        if (window.electronAPI && typeof window.electronAPI.closeInstanceBrowser === 'function') {
                            console.log(`关闭实例 ${instanceId} 的内部浏览器`);
                            window.electronAPI.closeInstanceBrowser(instanceId);
                        }
                    }
                }
            });

            settingsBtn.addEventListener('click', function() {
                console.log(`准备加载实例 ${instanceId} 的设置`);

                // 确保先清除当前引用，避免数据混淆
                document.getElementById('settingsModal').dataset.currentAppId = '';
                document.getElementById('settingsModal').dataset.currentInstanceId = '';
                document.getElementById('settingsModal').dataset.settingType = '';

                // 重置菜单状态 - 始终从通用设置开始显示
                document.querySelectorAll('.settings-menu-item').forEach(item => {
                    item.classList.remove('active');
                    if (item.textContent === '通用设置') {
                        item.classList.add('active');
                    }
                });

                // 重置内容区域显示
                document.getElementById('generalSettings').style.display = 'block';
                document.getElementById('aiSettings').style.display = 'none';

                // 初始化设置模态窗中的数据为默认值
                document.querySelectorAll('#generalSettings .settings-input')[0].value = '';
                document.querySelectorAll('#generalSettings .settings-input')[1].value = '';
                document.querySelectorAll('#aiSettings .settings-input').forEach(input => {
                    input.value = '';
                    input.dataset.originalValue = '';
                });

                // 载入该实例的设置
                loadInstanceSettings(instanceId);

                // 标记当前设置类型为实例设置
                document.getElementById('settingsModal').dataset.settingType = 'instance';

                // 更新激活界面
                document.querySelector('.activate-checkbox').innerHTML = `
                    <span class="custom-checkbox${window.instanceSettings[instanceId].activated ? ' checked' : ''}"></span>
                    激活 任务ID ${instanceId} 设置
                `;

                // 显示设置弹窗
                if (window.instanceSettings[instanceId].activated) {
                    // 已激活状态只显示设置界面
                    document.getElementById('settingsModal').classList.add('show');
                } else {
                    // 未激活状态同时显示激活界面和设置界面
                    document.getElementById('activateModal').classList.add('show');
                    document.getElementById('activateOverlay').classList.add('show');
                    document.getElementById('settingsModal').classList.add('show');
                }
            });

            deleteBtn.addEventListener('click', async function() {
                const shouldDelete = await showCustomAlert('确定要删除此实例吗？', true);
                if (shouldDelete) {
                    // 首先关闭实例的浏览器窗口
                    if (window.electronAPI && typeof window.electronAPI.closeInstanceBrowser === 'function') {
                        console.log(`关闭实例 ${instanceId} 的内部浏览器`);
                        window.electronAPI.closeInstanceBrowser(instanceId);
                    }

                    // 清除实例的会话数据
                    if (window.electronAPI && typeof window.electronAPI.clearInstanceSession === 'function') {
                        console.log(`清除实例 ${instanceId} 的会话数据`);
                        window.electronAPI.clearInstanceSession(instanceId);

                        // 注册回调函数以处理会话清除结果
                        window.electronAPI.onClearInstanceSessionReply(result => {
                            if (result.success) {
                                console.log(`实例 ${instanceId} 的会话数据已成功清除`);
                            } else {
                                console.error(`清除实例 ${instanceId} 的会话数据失败:`, result.error);
                            }
                        });
                    }

                    // 删除实例设置
                    delete window.instanceSettings[instanceId];

                    // 从本地存储中删除
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        categoryStorageAdapter.deleteInstance(instanceId);
                        console.log(`实例 ${instanceId} 已从本地存储中删除`);
                    }

                    // 删除实例元素
                    newInstance.remove();

                    // 如果没有实例了，显示空状态
                    if (document.querySelectorAll('.instance-item').length === 0) {
                        document.getElementById('emptyState').style.display = 'flex';
                    }
                }
            });
        }

        // 恢复实例列表函数
        function restoreInstanceList() {
            console.log('开始恢复实例列表...');

            // 清空实例容器，防止重复创建
            const instanceContainer = document.getElementById('instanceContainer');
            instanceContainer.innerHTML = '';

            // 隐藏空状态提示
            document.getElementById('emptyState').style.display = 'none';

            // 遍历所有实例设置
            for (const instanceId in window.instanceSettings) {
                // 创建新实例元素
                const newInstance = document.createElement('div');
                newInstance.className = 'instance-item';

                // 设置实例内容
                newInstance.innerHTML = `
                    <div class="instance-icon">
                        <i class="fab fa-tiktok"></i>
                    </div>
                    <div class="instance-details">
                        <div class="instance-info">
                            <div class="instance-id">任务ID: ${instanceId}</div>
                            <div class="instance-time">创建时间: ${window.instanceSettings[instanceId].createTime || '已恢复'}</div>
                        </div>
                    </div>
                    <div class="instance-actions">
                        <button class="toggle-btn${window.instanceSettings[instanceId].activated ? ' active' : ''}">
                            <span></span>
                        </button>
                        <div class="icon-btn" data-tooltip="设置">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="icon-btn" data-tooltip="删除">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                    </div>
                `;

                // 添加到实例容器
                document.getElementById('instanceContainer').appendChild(newInstance);

                // 绑定实例事件
                bindInstanceEvents(newInstance);
            }

            // 如果没有实例，显示空状态
            if (Object.keys(window.instanceSettings).length === 0) {
                document.getElementById('emptyState').style.display = 'flex';
            }

            console.log('已恢复实例列表，实例数量:', Object.keys(window.instanceSettings).length);
        }

        // 创建实例按钮点击事件 - 修改为使用应用设置作为模板
        document.addEventListener('DOMContentLoaded', function() {
            const createInstanceBtn = document.getElementById('createInstanceBtn');
            if (createInstanceBtn) {
                // 移除所有现有的点击事件监听器，防止重复添加
                const newCreateInstanceBtn = createInstanceBtn.cloneNode(true);
                createInstanceBtn.parentNode.replaceChild(newCreateInstanceBtn, createInstanceBtn);

                // 添加新的点击事件监听器
                newCreateInstanceBtn.addEventListener('click', async function() {
                    if (!window.selectedApp) {
                        await showCustomAlert('请先选择一个应用！');
                        return;
                    }

                    // 隐藏空状态提示
                    document.getElementById('emptyState').style.display = 'none';

                    // 创建新实例元素
                    const newInstance = document.createElement('div');
                    newInstance.className = 'instance-item';

                    // 获取当前时间
                    const now = new Date();
                    const timeStr = now.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });

                    const currentInstanceId = window.instanceCounter;

                    // 设置实例内容
                    newInstance.innerHTML = `
                        <div class="instance-icon">
                            <i class="fab fa-tiktok"></i>
                        </div>
                        <div class="instance-details">
                            <div class="instance-info">
                                <div class="instance-id">任务ID: ${currentInstanceId}</div>
                                <div class="instance-time">创建时间: ${timeStr}</div>
                            </div>
                        </div>
                        <div class="instance-actions">
                            <button class="toggle-btn" data-tooltip="开启/关闭"></button>
                            <div class="icon-btn" data-tooltip="设置">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="icon-btn" data-tooltip="删除">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                        </div>
                    `;

                    // 将新实例添加到容器
                    document.getElementById('instanceContainer').appendChild(newInstance);

                    // 初始化实例设置 - 使用所选应用作为模板
                    initInstanceSettings(currentInstanceId, window.selectedApp);

                    // 绑定实例操作按钮事件
                    bindInstanceEvents(newInstance);

                    // 实例计数器增加
                    window.instanceCounter++;

                    // 查看所有实例的设置数据（调试用）
                    console.log('所有实例的设置数据:', JSON.parse(JSON.stringify(window.instanceSettings)));
                });
            }

            console.log('已添加创建实例按钮的点击事件监听器');
        });

        // 自定义复选框
        document.querySelectorAll('.checkbox-label').forEach(label => {
            label.addEventListener('click', function(e) {
                const checkbox = this.querySelector('.custom-checkbox');
                checkbox.classList.toggle('checked');
            });
        });

        // 日志按钮切换
        document.addEventListener('DOMContentLoaded', function() {
            // 清空日志按钮
            const clearLogBtn = document.getElementById('clearLogBtn');
            if (clearLogBtn && !clearLogBtn.hasAttribute('data-event-attached')) {
                // 使用自定义属性标记事件已附加，防止重复注册
                clearLogBtn.setAttribute('data-event-attached', 'true');
                
                clearLogBtn.addEventListener('click', function() {
                    if (window.electronAPI && typeof window.electronAPI.clearLogs === 'function') {
                        // 使用自定义弹窗替换原生confirm
                        showCustomAlert('确定要清空所有日志吗？', true).then(shouldClear => {
                            if (shouldClear) {
                                window.electronAPI.clearLogs();
                                window.logs = [];
                                updateLogsDisplay();
                                console.log('已清空所有日志');
                            }
                        });
                    }
                });
            }

            // 打开日志文件夹按钮
            const openLogFolderBtn = document.getElementById('openLogFolderBtn');
            if (openLogFolderBtn && !openLogFolderBtn.hasAttribute('data-event-attached')) {
                // 使用自定义属性标记事件已附加，防止重复注册
                openLogFolderBtn.setAttribute('data-event-attached', 'true');

                // 使用防抖函数包装事件处理程序，防止短时间内多次触发
                const openLogFolder = debounce(async function(e) {
                    // 阻止事件冒泡和默认行为
                    e.preventDefault();
                    e.stopPropagation();

                    // 禁用按钮，防止重复点击
                    this.disabled = true;

                    if (window.electronAPI && typeof window.electronAPI.openBrowser === 'function' && typeof window.electronAPI.getAppDataPath === 'function') {
                        try {
                            // 获取应用的数据目录（等待Promise解析）
                            const appDataPath = await window.electronAPI.getAppDataPath();
                            if (appDataPath) {
                                // 使用shell.openPath直接打开文件夹
                                if (typeof window.electronAPI.openFolder === 'function') {
                                    const logFolderPath = `${appDataPath}\\data\\logs`;
                                    window.electronAPI.openFolder(logFolderPath);
                                    console.log('已打开日志文件夹:', logFolderPath);
                                } else {
                                    // 备用方案：使用openBrowser打开
                                    const fileUrl = `file:///${appDataPath.replace(/\\/g, '/')}/data/logs`;
                                    window.electronAPI.openBrowser(fileUrl);
                                    console.log('已打开日志文件夹URL:', fileUrl);
                                }
                            } else {
                                console.error('获取应用数据路径失败');
                                alert('无法获取日志文件夹路径');
                            }
                        } catch (error) {
                            console.error('打开日志文件夹出错:', error);
                            alert('打开日志文件夹失败: ' + error.message);
                        } finally {
                            // 延迟一段时间后重新启用按钮
                            setTimeout(() => {
                                this.disabled = false;
                            }, 1000);
                        }
                    } else {
                        console.error('缺少必要的API函数');
                        alert('此功能在当前环境不可用');
                        this.disabled = false;
                    }
                }, 500); // 500毫秒的防抖延迟

                openLogFolderBtn.addEventListener('click', openLogFolder);
            }

            // 运行日志按钮
            const runningLogBtn = document.getElementById('runningLogBtn');
            if (runningLogBtn) {
                runningLogBtn.addEventListener('click', function() {
                    // 检查当前按钮文本，判断当前状态
                    const currentText = this.innerText.trim();
                    const viewMode = this.dataset.viewMode || '';

                    // 重置查看所有日志的标记
                    window.viewingAllLogs = false;

                    // 获取当前状态下的第一个用户
                    const firstUser = getFirstMonitoredUser();

                    if (currentText === '实例 1' || currentText === '实例 2' || currentText === '实例 3' ||
                        currentText === '实例 4' || currentText === '实例 5' || currentText === '实例 6') {
                        // 当前是实例状态，点击后显示该实例首位用户的日志
                        if (firstUser) {
                            filterLogsByUser(firstUser);
                        } else {
                            // 没有用户，保持实例状态
                            displayAllMonitoredUsersLogs();
                        }
                    } else if (viewMode === 'all-logs') {
                        // 当前是所有日志状态，点击后显示所有用户状态时首位用户的日志
                        // 重置实例过滤
                        window.currentLogInstanceId = null;
                        window.currentFilteredUsername = null;

                        // 更新用户列表显示
                        updateMonitoredUsersDisplay();

                        // 获取所有用户状态下的第一个用户
                        const allFirstUser = getFirstMonitoredUser();
                        if (allFirstUser) {
                            filterLogsByUser(allFirstUser);
                        } else {
                            // 没有用户，显示运行日志
                            updateLogsDisplay();
                            this.innerHTML = `<i class="fas fa-list-alt"></i>运行日志`;
                            delete this.dataset.viewMode;
                        }
                    } else {
                        // 其他状态（运行日志、用户日志等），点击后显示当前状态时首位用户的日志
                        if (firstUser) {
                            filterLogsByUser(firstUser);
                        } else {
                            // 没有用户，显示运行日志
                            updateLogsDisplay();
                            this.innerHTML = `<i class="fas fa-list-alt"></i>运行日志`;
                            delete this.dataset.viewMode;
                        }
                    }

                    // 激活样式
                    document.querySelectorAll('.log-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            }
        });

        // 帮助弹窗关闭功能
        document.addEventListener('DOMContentLoaded', function() {
            const helpModalClose = document.querySelector('.help-modal-close');
            if (helpModalClose) {
                helpModalClose.addEventListener('click', function() {
                    document.getElementById('helpModal').classList.remove('show');
                    document.getElementById('helpOverlay').classList.remove('show');
                });
            }
        });

        // 激活复选框点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const activateCheckbox = document.getElementById('activateCheckbox');
            if (activateCheckbox) {
                activateCheckbox.addEventListener('click', function() {
                    // 获取当前编辑的ID和类型
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    const settingType = document.getElementById('settingsModal').dataset.settingType;

                    // 应用设置不使用激活功能，忽略
                    if (settingType === 'app') {
                        return;
                    }

                    const checkbox = this.querySelector('.custom-checkbox');
                    const deactivateBtn = document.querySelector('.settings-deactivate');

                    checkbox.classList.add('checked');
                    this.classList.add('checked');

                    // 更新取消激活按钮状态
                    deactivateBtn.classList.remove('not-activated');
                    deactivateBtn.classList.add('activated');

                    // 更新设置
                    if (settingType === 'instance' && instanceId) {
                        window.instanceSettings[instanceId].activated = true;
                        console.log(`激活实例 ${instanceId} 设置`);

                        // 保存到本地存储
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            categoryStorageAdapter.saveInstance(instanceId, window.instanceSettings[instanceId]);
                            console.log(`实例 ${instanceId} 的激活状态已保存到本地存储`);
                        }

                        // 找到并触发对应实例的开关按钮点击事件，以确保行为一致
                        try {
                            // 更精确地找到对应实例的开关按钮
                            const instanceItems = document.querySelectorAll('.instance-item');
                            let instanceToggleBtn = null;

                            for (const item of instanceItems) {
                                const idElement = item.querySelector('.instance-id');
                                if (idElement && idElement.textContent.includes(instanceId)) {
                                    instanceToggleBtn = item.querySelector('.toggle-btn');
                                    break;
                                }
                            }

                            if (instanceToggleBtn) {
                                // 检查按钮当前状态
                                const isActive = instanceToggleBtn.classList.contains('active');
                                console.log(`通过实例激活界面找到实例 ${instanceId} 的开关按钮，当前状态: ${isActive ? '已激活' : '未激活'}`);

                                // 只有在按钮未激活时才触发点击
                                if (!isActive) {
                                    console.log(`模拟点击实例 ${instanceId} 的开关按钮`);

                                    // 创建一个新的点击事件
                                    const clickEvent = new MouseEvent('click', {
                                        bubbles: true,
                                        cancelable: true,
                                        view: window
                                    });

                                    // 分发事件到按钮 - 注意这里不预先添加active类，让事件处理程序完成这个工作
                                    instanceToggleBtn.dispatchEvent(clickEvent);

                                    console.log(`已触发实例 ${instanceId} 的开关按钮点击事件`);
                                } else {
                                    console.log(`实例 ${instanceId} 的开关按钮已经处于激活状态，无需再次触发`);
                                }
                            } else {
                                console.error(`无法找到实例 ${instanceId} 的开关按钮`);
                            }
                        } catch (error) {
                            console.error(`无法找到或触发实例 ${instanceId} 的开关按钮:`, error);
                        }
                    }

                    // 只关闭激活界面
                    document.getElementById('activateModal').classList.remove('show');
                    document.getElementById('activateOverlay').classList.remove('show');
                });
            }
        });

        // 设置界面关闭按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const settingsClose = document.querySelector('.settings-close');
            if (settingsClose) {
                settingsClose.addEventListener('click', function() {
                    // 获取当前编辑的ID
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    const appId = document.getElementById('settingsModal').dataset.currentAppId;
                    const settingType = document.getElementById('settingsModal').dataset.settingType;

                    if (settingType === 'app' && appId) {
                        // 保存应用设置
                        // 检查当前激活的选项卡，如果是通用设置，则保存通用设置
                        const activeTab = document.querySelector('.settings-menu-item.active').textContent;
                        if (activeTab === '通用设置') {
                            try {
                                // 获取当前的通用设置值
                                const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                                const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                                // 创建当前应用设置的深拷贝
                                const updatedSettings = JSON.parse(JSON.stringify(window.appSettings[appId]));

                                // 更新通用设置
                                updatedSettings.general = {
                                    defaultReply: defaultReply,
                                    transferReply: transferReply
                                };

                                // 保存更新后的设置
                                window.appSettings[appId] = updatedSettings;

                                // 保存到本地存储
                                if (typeof categoryStorageAdapter !== 'undefined') {
                                    categoryStorageAdapter.saveApp(appId, updatedSettings);
                                    console.log(`应用 ${appId} 的设置已保存到本地存储`);
                                }

                                console.log('关闭设置窗口时保存应用通用设置:', JSON.parse(JSON.stringify(window.appSettings[appId])));
                            } catch (error) {
                                console.error('关闭设置窗口时保存应用通用设置失败:', error);
                            }
                        }
                    } else if (settingType === 'instance' && instanceId) {
                        // 保存实例设置
                        // 检查当前激活的选项卡，如果是通用设置，则保存通用设置
                        const activeTab = document.querySelector('.settings-menu-item.active').textContent;
                        if (activeTab === '通用设置') {
                            try {
                                // 获取当前的通用设置值
                                const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                                const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                                // 创建当前实例设置的深拷贝
                                const updatedSettings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));

                                // 更新通用设置
                                updatedSettings.general = {
                                    defaultReply: defaultReply,
                                    transferReply: transferReply
                                };

                                // 保存更新后的设置
                                window.instanceSettings[instanceId] = updatedSettings;

                                // 保存到本地存储
                                if (typeof categoryStorageAdapter !== 'undefined') {
                                    categoryStorageAdapter.saveInstance(instanceId, updatedSettings);
                                    console.log(`实例 ${instanceId} 的设置已保存到本地存储`);
                                }

                                console.log('关闭设置窗口时保存实例通用设置:', JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));
                            } catch (error) {
                                console.error('关闭设置窗口时保存实例通用设置失败:', error);
                            }
                        }
                    }

                    // 关闭设置弹窗
                    document.getElementById('settingsModal').classList.remove('show');

                    // 清除当前引用以避免数据混淆
                    document.getElementById('settingsModal').dataset.currentInstanceId = '';
                    document.getElementById('settingsModal').dataset.currentAppId = '';
                    document.getElementById('settingsModal').dataset.settingType = '';

                    // 重置取消激活按钮的显示状态 - 确保下次显示
                    document.querySelector('.settings-deactivate').style.display = '';
                });
            }
        });

        // 设置界面菜单切换
        document.querySelectorAll('.settings-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // 获取当前编辑的ID
                const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                const appId = document.getElementById('settingsModal').dataset.currentAppId;
                const settingType = document.getElementById('settingsModal').dataset.settingType;

                if (!settingType || (settingType === 'instance' && !instanceId) || (settingType === 'app' && !appId)) {
                    console.error('无法保存设置：ID无效');
                    return;
                }

                // 在切换菜单前保存当前菜单的设置
                const currentTab = document.querySelector('.settings-menu-item.active').textContent;
                if (currentTab === '通用设置' && this.textContent === 'AI配置') {
                    if (settingType === 'app') {
                        // 从通用设置切换到AI配置，保存应用通用设置
                        console.log(`切换到AI配置前保存应用通用设置: 应用ID ${appId}`);

                        // 获取当前的通用设置值
                        const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                        const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                        // 创建新的设置对象以避免引用问题
                        const updatedSettings = JSON.parse(JSON.stringify(window.appSettings[appId]));

                        // 确保general对象存在
                        if (!updatedSettings.general) {
                            updatedSettings.general = {};
                        }

                        // 更新通用设置
                        updatedSettings.general.defaultReply = defaultReply;
                        updatedSettings.general.transferReply = transferReply;

                        // 保存更新后的设置对象
                        window.appSettings[appId] = updatedSettings;

                        // 保存到本地存储
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            categoryStorageAdapter.saveApp(appId, updatedSettings);
                            console.log(`应用 ${appId} 的设置已保存到本地存储`);
                        }

                        console.log('保存后的应用设置:', JSON.parse(JSON.stringify(window.appSettings[appId])));
                    } else if (settingType === 'instance') {
                        // 从通用设置切换到AI配置，保存实例通用设置
                        console.log(`切换到AI配置前保存实例通用设置: 实例ID ${instanceId}`);

                        // 获取当前的通用设置值
                        const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                        const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                        // 创建新的设置对象以避免引用问题
                        const updatedSettings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));

                        // 确保general对象存在
                        if (!updatedSettings.general) {
                            updatedSettings.general = {};
                        }

                        // 更新通用设置
                        updatedSettings.general.defaultReply = defaultReply;
                        updatedSettings.general.transferReply = transferReply;

                        // 保存更新后的设置对象
                        window.instanceSettings[instanceId] = updatedSettings;

                        // 保存到本地存储
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            categoryStorageAdapter.saveInstance(instanceId, updatedSettings);
                            console.log(`实例 ${instanceId} 的设置已保存到本地存储`);
                        }

                        console.log('保存后的实例设置:', JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));
                    }
                }

                // 移除所有菜单项的active类
                document.querySelectorAll('.settings-menu-item').forEach(i => i.classList.remove('active'));
                // 为当前点击的菜单项添加active类
                this.classList.add('active');

                // 根据点击的菜单项显示对应的内容
                const isAISettings = this.textContent === 'AI配置';
                document.getElementById('generalSettings').style.display = isAISettings ? 'none' : 'block';
                document.getElementById('aiSettings').style.display = isAISettings ? 'block' : 'none';
            });
        });

        // 取消激活按钮点击事件 - 应用设置不使用此功能
        document.addEventListener('DOMContentLoaded', function() {
            const settingsDeactivate = document.querySelector('.settings-deactivate');
            if (settingsDeactivate) {
                settingsDeactivate.addEventListener('click', function() {
                    // 获取当前编辑的ID和类型
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    const appId = document.getElementById('settingsModal').dataset.currentAppId;
                    const settingType = document.getElementById('settingsModal').dataset.settingType;

                    // 如果是应用设置，不应该看到此按钮，忽略点击
                    if (settingType === 'app') {
                        return;
                    }

                    // 以下代码只应用于实例设置
                    if (this.classList.contains('activated')) {
                        // 显示激活界面
                        document.getElementById('activateModal').classList.add('show');
                        document.getElementById('activateOverlay').classList.add('show');

                        // 重置激活复选框状态
                        const activateCheckbox = document.getElementById('activateCheckbox');
                        const checkbox = activateCheckbox.querySelector('.custom-checkbox');
                        checkbox.classList.remove('checked');
                        activateCheckbox.classList.remove('checked');

                        // 更新按钮状态
                        this.classList.remove('activated');
                        this.classList.add('not-activated');

                        // 更新设置
                        if (settingType === 'instance' && instanceId) {
                            window.instanceSettings[instanceId].activated = false;
                            console.log(`取消激活实例 ${instanceId} 设置`);

                            // 保存到本地存储
                            if (typeof categoryStorageAdapter !== 'undefined') {
                                categoryStorageAdapter.saveInstance(instanceId, window.instanceSettings[instanceId]);
                                console.log(`实例 ${instanceId} 的取消激活状态已保存到本地存储`);
                            }
                        }
                    } else if (this.classList.contains('not-activated')) {
                        // 如果按钮处于未激活状态，点击时显示激活界面
                        document.getElementById('activateModal').classList.add('show');
                        document.getElementById('activateOverlay').classList.add('show');

                        // 更新激活复选框的文本
                        if (settingType === 'instance' && instanceId) {
                            document.querySelector('.activate-checkbox').innerHTML = `
                                <span class="custom-checkbox"></span>
                                激活 任务ID ${instanceId} 设置
                            `;
                        }
                    }
                });
            }
        });

        // 设置界面关闭按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const settingsClose = document.querySelector('.settings-close');
            if (settingsClose) {
                settingsClose.addEventListener('click', function() {
                    // 获取当前编辑的ID
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    const appId = document.getElementById('settingsModal').dataset.currentAppId;
                    const settingType = document.getElementById('settingsModal').dataset.settingType;

                    if (settingType === 'app' && appId) {
                        // 保存应用设置
                        // 检查当前激活的选项卡，如果是通用设置，则保存通用设置
                        const activeTab = document.querySelector('.settings-menu-item.active').textContent;
                        if (activeTab === '通用设置') {
                            try {
                                // 获取当前的通用设置值
                                const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                                const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                                // 创建当前应用设置的深拷贝
                                const updatedSettings = JSON.parse(JSON.stringify(window.appSettings[appId]));

                                // 更新通用设置
                                updatedSettings.general = {
                                    defaultReply: defaultReply,
                                    transferReply: transferReply
                                };

                                // 保存更新后的设置
                                window.appSettings[appId] = updatedSettings;

                                console.log('关闭设置窗口时保存应用通用设置:', JSON.parse(JSON.stringify(window.appSettings[appId])));
                            } catch (error) {
                                console.error('关闭设置窗口时保存应用通用设置失败:', error);
                            }
                        }
                    } else if (settingType === 'instance' && instanceId) {
                        // 保存实例设置
                        // 检查当前激活的选项卡，如果是通用设置，则保存通用设置
                        const activeTab = document.querySelector('.settings-menu-item.active').textContent;
                        if (activeTab === '通用设置') {
                            try {
                                // 获取当前的通用设置值
                                const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                                const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                                // 创建当前实例设置的深拷贝
                                const updatedSettings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));

                                // 更新通用设置
                                updatedSettings.general = {
                                    defaultReply: defaultReply,
                                    transferReply: transferReply
                                };

                                // 保存更新后的设置
                                window.instanceSettings[instanceId] = updatedSettings;

                                console.log('关闭设置窗口时保存实例通用设置:', JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));
                            } catch (error) {
                                console.error('关闭设置窗口时保存实例通用设置失败:', error);
                            }
                        }
                    }

                    // 关闭设置弹窗
                    document.getElementById('settingsModal').classList.remove('show');

                    // 清除当前引用以避免数据混淆
                    document.getElementById('settingsModal').dataset.currentInstanceId = '';
                    document.getElementById('settingsModal').dataset.currentAppId = '';
                    document.getElementById('settingsModal').dataset.settingType = '';

                    // 重置取消激活按钮的显示状态 - 确保下次显示
                    document.querySelector('.settings-deactivate').style.display = '';
                });
            }
        });

        // 在 JavaScript 中修改取消激活按钮的初始文本
        document.addEventListener('DOMContentLoaded', function() {
            const settingsDeactivate = document.querySelector('.settings-deactivate');
            if (settingsDeactivate) {
                settingsDeactivate.textContent = '取消激活';
            }
        });

        // 修改添加按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyAddBtn = document.querySelector('.edit-reply-add-btn');
            if (editReplyAddBtn) {
                editReplyAddBtn.addEventListener('click', function() {
                    const modal = document.getElementById('editReplyModal');
                    const textarea = document.querySelector('.edit-reply-textarea');
                    const content = textarea.value.trim();

                    if (!content) {
                        showCustomAlert('请输入回复内容');
                        return;
                    }

                    // 检查是否正在编辑现有回复
                    const editingItem = document.querySelector('.edit-reply-shortcut.editing');
                    if (editingItem) {
                        // 更新现有回复的内容
                        editingItem.innerHTML = `
                            ${content}
                            <i class="fas fa-times delete-icon"></i>
                        `;
                        // 重新绑定删除事件
                        editingItem.querySelector('.delete-icon').addEventListener('click', function(e) {
                            e.stopPropagation();
                            editingItem.remove();
                        });
                        // 移除编辑状态
                        editingItem.classList.remove('editing');
                    } else {
                        // 创建新的回复项
                        const shortcut = document.createElement('div');
                        shortcut.className = 'edit-reply-shortcut';
                        shortcut.innerHTML = `
                            ${content}
                            <i class="fas fa-times delete-icon"></i>
                        `;

                        // 添加点击事件，将内容加载到编辑框
                        shortcut.addEventListener('click', function(e) {
                            // 如果点击的是删除图标，不执行加载操作
                            if (e.target.classList.contains('delete-icon')) {
                                return;
                            }

                            // 如果当前项已经在编辑状态，则取消编辑
                            if (this.classList.contains('editing')) {
                                this.classList.remove('editing');
                                textarea.value = '';
                                document.querySelector('.edit-reply-counter').textContent = '0/800';
                                return;
                            }

                            // 否则进入编辑状态
                            textarea.value = content;
                            textarea.focus();
                            // 更新字数统计
                            document.querySelector('.edit-reply-counter').textContent = `${content.length}/800`;
                            // 标记当前正在编辑的回复项
                            document.querySelectorAll('.edit-reply-shortcut').forEach(item => item.classList.remove('editing'));
                            this.classList.add('editing');
                        });

                        // 添加删除事件
                        shortcut.querySelector('.delete-icon').addEventListener('click', function(e) {
                            e.stopPropagation();
                            shortcut.remove();
                        });

                        document.querySelector('.edit-reply-shortcuts').appendChild(shortcut);
                    }

                    // 清空输入框
                    textarea.value = '';
                    document.querySelector('.edit-reply-counter').textContent = '0/800';
                });
            }
        });

        // 添加保存内容的函数
        function saveReplyContent() {
            // 获取当前编辑的实例ID
            const modal = document.getElementById('editReplyModal');
            const instanceId = modal.dataset.currentInstanceId;

            if (!instanceId || !instanceSettings[instanceId]) return;

            const shortcuts = modal.querySelectorAll('.edit-reply-shortcut');

            if (shortcuts.length === 0) {
                return;
            }

            // 将所有回复内容用【or】连接
            const replyContents = Array.from(shortcuts).map(shortcut => {
                return shortcut.childNodes[0].textContent.trim();
            });
            const combinedContent = replyContents.join('【or】');

            // 确保general对象存在
            if (!instanceSettings[instanceId].general) {
                instanceSettings[instanceId].general = {
                    defaultReply: "当前消息有点多，我稍后再回复您【or】您好，有什么能帮助...",
                    transferReply: "已为您转接其他客服"
                };
            }

            // 根据当前编辑的类型更新对应的输入框和实例设置
            const isTransferReply = modal.dataset.targetInput === 'transfer';

            if (isTransferReply) {
                // 更新转人工默认回复
                instanceSettings[instanceId].general.transferReply = combinedContent;
                // 更新对应的UI输入框
                document.querySelectorAll('#generalSettings .settings-input')[1].value = combinedContent;
            } else {
                // 更新默认回复
                instanceSettings[instanceId].general.defaultReply = combinedContent;
                // 更新对应的UI输入框
                document.querySelectorAll('#generalSettings .settings-input')[0].value = combinedContent;
            }
        }

        // 编辑弹窗关闭按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyClose = document.querySelector('.edit-reply-close');
            if (editReplyClose) {
                editReplyClose.addEventListener('click', function() {
                    const modal = document.getElementById('editReplyModal');

                    // 清除各种引用，避免数据混淆
                    modal.dataset.currentInstanceId = '';
                    modal.dataset.currentAppId = '';
                    modal.dataset.settingType = '';
                    modal.dataset.targetInput = '';

                    // 关闭弹窗
                    modal.style.display = 'none';
                    document.getElementById('helpOverlay').classList.remove('show');
                });
            }
        });

        // 编辑弹窗取消按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyBtnCancel = document.querySelector('.edit-reply-modal .edit-reply-btn.cancel');
            if (editReplyBtnCancel) {
                editReplyBtnCancel.addEventListener('click', function() {
                    const modal = document.getElementById('editReplyModal');

                    // 清除各种引用，避免数据混淆
                    modal.dataset.currentInstanceId = '';
                    modal.dataset.currentAppId = '';
                    modal.dataset.settingType = '';
                    modal.dataset.targetInput = '';

                    // 关闭编辑弹窗
                    modal.style.display = 'none';
                    document.getElementById('helpOverlay').classList.remove('show');
                });
            }
        });

        // 文本框字数统计
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyTextarea = document.querySelector('.edit-reply-textarea');
            if (editReplyTextarea) {
                editReplyTextarea.addEventListener('input', function() {
                    const counter = document.querySelector('.edit-reply-counter');
                    if (counter) {
                        counter.textContent = `${this.value.length}/800`;
                    }
                });
            }
        });

        // 编辑按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const settingsBtns = document.querySelectorAll('.settings-btn');
            if (settingsBtns.length > 0) {
                // 移除所有现有的点击事件监听器，防止重复添加
                const newSettingsBtns = [];
                settingsBtns.forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                    newSettingsBtns.push(newBtn);
                });

                // 添加新的点击事件监听器
                newSettingsBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // 获取当前编辑的ID和类型
                        const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                        const appId = document.getElementById('settingsModal').dataset.currentAppId;
                        const settingType = document.getElementById('settingsModal').dataset.settingType;

                        console.log('编辑按钮点击事件触发，当前数据:', {
                            settingType: settingType,
                            instanceId: instanceId,
                            appId: appId
                        });

                        if (!settingType || (settingType === 'instance' && !instanceId) || (settingType === 'app' && !appId)) {
                            console.error('无法编辑回复内容：ID无效');
                            showCustomAlert('无法编辑回复内容：ID无效');
                            return;
                        }

                                console.log(`开始编辑${settingType === 'app' ? '应用' : '实例'}回复内容, ID: ${settingType === 'app' ? appId : instanceId}`);

                                const inputGroup = this.closest('.settings-input-group');
                                const input = inputGroup.querySelector('.settings-input');
                                const isTransferReply = inputGroup === document.querySelectorAll('.settings-input-group')[1]; // 第二个输入组是转人工回复
                                const modal = document.getElementById('editReplyModal');

                                console.log('编辑的是转人工回复?', isTransferReply);

                                // 设置标题
                                modal.querySelector('.edit-reply-title').textContent = isTransferReply ? '编辑转人工默认回复' : '编辑默认回复';

                                // 清空现有的快捷回复列表
                                const shortcutsContainer = modal.querySelector('.edit-reply-shortcuts');
                                shortcutsContainer.innerHTML = '';

                                // 获取设置数据
                                let settings;
                                if (settingType === 'app') {
                                    // 使用深拷贝获取应用设置，避免引用问题
                                    settings = JSON.parse(JSON.stringify(window.appSettings[appId]));
                                    console.log('当前应用的设置数据:', settings);
                                } else if (settingType === 'instance') {
                                    // 使用深拷贝获取实例设置，避免引用问题
                                    settings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));
                                    console.log('当前实例的设置数据:', settings);
                                }

                                // 从设置中获取回复内容
                                let replyContent = isTransferReply
                                    ? settings.general.transferReply
                                    : settings.general.defaultReply;

                                console.log(`从${settingType}设置加载${isTransferReply ? '转人工' : '默认'}回复内容:`, replyContent);

                                // 按【or】分割
                                const replies = replyContent.split('【or】').map(reply => reply.trim()).filter(reply => reply);

                                // 添加快捷回复项
                                replies.forEach(reply => {
                                    const shortcut = document.createElement('div');
                                    shortcut.className = 'edit-reply-shortcut';
                                    shortcut.innerHTML = `
                                        ${reply}
                                        <i class="fas fa-times delete-icon"></i>
                                    `;

                                    // 添加点击事件，将内容加载到编辑框
                                    shortcut.addEventListener('click', function(e) {
                                        // 如果点击的是删除图标，不执行加载操作
                                        if (e.target.classList.contains('delete-icon')) {
                                            return;
                                        }

                                        // 如果当前项已经在编辑状态，则取消编辑
                                        if (this.classList.contains('editing')) {
                                            this.classList.remove('editing');
                                            const textarea = modal.querySelector('.edit-reply-textarea');
                                            textarea.value = '';
                                            modal.querySelector('.edit-reply-counter').textContent = '0/800';
                                            return;
                                        }

                                        // 否则进入编辑状态
                                        textarea.value = reply;
                                        textarea.focus();
                                        // 更新字数统计
                                        modal.querySelector('.edit-reply-counter').textContent = `${reply.length}/800`;
                                        // 标记当前正在编辑的回复项
                                        shortcutsContainer.querySelectorAll('.edit-reply-shortcut').forEach(item => item.classList.remove('editing'));
                                        this.classList.add('editing');
                                    });

                                    // 添加删除事件
                                    shortcut.querySelector('.delete-icon').addEventListener('click', function(e) {
                                        e.stopPropagation();
                                        shortcut.remove();
                                    });

                                    shortcutsContainer.appendChild(shortcut);
                                });

                                // 清空并重置文本框
                                const textarea = modal.querySelector('.edit-reply-textarea');
                                textarea.value = '';
                                modal.querySelector('.edit-reply-counter').textContent = '0/800';

                                // 保存当前编辑的信息到弹窗
                                modal.dataset.targetInput = isTransferReply ? 'transfer' : 'default';
                                modal.dataset.currentInstanceId = settingType === 'instance' ? instanceId : '';
                                modal.dataset.currentAppId = settingType === 'app' ? appId : '';
                                modal.dataset.settingType = settingType;

                                console.log(`编辑弹窗已准备: settingType=${settingType}, ID=${settingType === 'app' ? appId : instanceId}, targetInput=${modal.dataset.targetInput}`);

                                // 显示弹窗
                                modal.style.display = 'block';
                                document.getElementById('helpOverlay').classList.add('show');
                    });
                });
            }
        });


        // 在script标签中添加AI配置输入框的自动保存功能
        document.addEventListener('DOMContentLoaded', function() {
            const aiSettingsInputs = document.querySelectorAll('#aiSettings .settings-input');
            if (aiSettingsInputs.length > 0) {
                aiSettingsInputs.forEach(input => {
                    // 保存输入框的初始值
                    input.dataset.originalValue = input.value;
                });
            }

            // 添加单选按钮的值初始化
            const aiModeRadios = document.querySelectorAll('input[name="aiMode"]');
            if (aiModeRadios.length > 0) {
                aiModeRadios.forEach(radio => {
                    if(radio.checked) {
                        radio.dataset.originalChecked = "true";
                    }
                });
            }
        });

        // AI配置保存按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const aiSettingsSave = document.getElementById('aiSettingsSave');
            if (aiSettingsSave) {
                aiSettingsSave.addEventListener('click', function() {
                    // 获取当前编辑的ID和类型
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    const appId = document.getElementById('settingsModal').dataset.currentAppId;
                    const settingType = document.getElementById('settingsModal').dataset.settingType;

                    if (!settingType || (settingType === 'instance' && !instanceId) || (settingType === 'app' && !appId)) {
                        console.error('无法保存AI配置：ID无效');
                        showCustomAlert('无法保存设置：ID无效');
                        return;
                    }

                    console.log(`正在保存AI配置: ${settingType === 'app' ? '应用ID ' + appId : '实例ID ' + instanceId}`);

                    // 保存所有输入框的当前值作为原始值（用于重置）
                    const aiInputs = document.querySelectorAll('#aiSettings .settings-input');
                    aiInputs.forEach(input => {
                        input.dataset.originalValue = input.value;
                    });

                    // 保存单选按钮的选中状态（用于重置）
                    const selectedMode = document.querySelector('input[name="aiMode"]:checked').value;
                    document.querySelectorAll('input[name="aiMode"]').forEach(radio => {
                        radio.dataset.originalChecked = radio.checked ? "true" : "false";
                    });

                    try {
                        if (settingType === 'app') {
                            // 保存应用AI配置
                            // 确保当前应用设置存在
                            if (!window.appSettings[appId]) {
                                initAppSettings(appId);
                            }

                            // 1. 获取当前应用的完整设置的深拷贝
                            const currentSettings = JSON.parse(JSON.stringify(window.appSettings[appId]));

                            // 2. 只更新AI相关的属性
                            currentSettings.ai = {
                                mode: selectedMode,
                                apiAddress: aiInputs[0].value,
                                appId: aiInputs[1].value,
                                apiKey: aiInputs[2].value
                            };

                            // 3. 更新完整的应用设置
                            window.appSettings[appId] = currentSettings;

                            console.log('保存后的应用设置:', JSON.parse(JSON.stringify(window.appSettings[appId])));
                        } else if (settingType === 'instance') {
                            // 保存实例AI配置
                            // 确保当前实例设置存在
                            if (!window.instanceSettings[instanceId]) {
                                initInstanceSettings(instanceId);
                            }

                            // 1. 获取当前实例的完整设置的深拷贝
                            const currentSettings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));

                            // 2. 只更新AI相关的属性
                            currentSettings.ai = {
                                mode: selectedMode,
                                apiAddress: aiInputs[0].value,
                                appId: aiInputs[1].value,
                                apiKey: aiInputs[2].value
                            };

                            // 3. 更新完整的实例设置
                            window.instanceSettings[instanceId] = currentSettings;

                            console.log('保存后的实例设置:', JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));
                        }

                        // 显示保存成功提示
                        showCustomAlert('保存成功');
                    } catch (error) {
                        console.error('保存AI配置时发生错误:', error);
                        showCustomAlert('保存失败，请重试');
                    }
                });
            }
        });

        // AI配置重置按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const aiSettingsReset = document.getElementById('aiSettingsReset');
            if (aiSettingsReset) {
                aiSettingsReset.addEventListener('click', function() {
                    // 获取当前编辑的实例ID
                    const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                    if (!instanceId) return;

                    // 将所有输入框恢复为原始值
                    document.querySelectorAll('#aiSettings .settings-input').forEach(input => {
                        if(input.dataset.originalValue) {
                            input.value = input.dataset.originalValue;
                        }
                    });

                    // 恢复单选按钮的原始选中状态
                    document.querySelectorAll('input[name="aiMode"]').forEach(radio => {
                        radio.checked = radio.dataset.originalChecked === "true";
                    });
                });
            }
        });

        // 在script标签中添加激活状态管理
        window.activatedInstances = new Set();

        // 保存按钮点击事件已移至下方的完全重写版本

        // 激活界面点击空白区域关闭
        document.addEventListener('DOMContentLoaded', function() {
            const activateOverlay = document.getElementById('activateOverlay');
            if (activateOverlay) {
                activateOverlay.addEventListener('click', function() {
                    document.getElementById('activateModal').classList.remove('show');
                    document.getElementById('activateOverlay').classList.remove('show');

                    // 更新按钮状态为未激活
                    const deactivateBtn = document.querySelector('.settings-deactivate');
                    if (deactivateBtn) {
                        deactivateBtn.classList.remove('activated');
                        deactivateBtn.classList.add('not-activated');
                    }
                });
            }
        });

        // 编辑弹窗保存按钮点击事件 - 完全重写
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyModalSaveBtn = document.querySelector('.edit-reply-modal .edit-reply-btn.save');
            if (editReplyModalSaveBtn) {
                // 移除所有现有的点击事件监听器，防止重复添加
                const newEditReplyModalSaveBtn = editReplyModalSaveBtn.cloneNode(true);
                editReplyModalSaveBtn.parentNode.replaceChild(newEditReplyModalSaveBtn, editReplyModalSaveBtn);

                // 添加新的点击事件监听器
                newEditReplyModalSaveBtn.addEventListener('click', function() {
                    const modal = document.getElementById('editReplyModal');
                    const instanceId = modal.dataset.currentInstanceId; // 从模态框获取实例ID
                    const appId = modal.dataset.currentAppId; // 从模态框获取应用ID
                    const settingType = modal.dataset.settingType; // 从模态框获取设置类型

                    // 输出调试信息，帮助识别问题
                    console.log('当前编辑弹窗数据:', {
                        settingType: settingType,
                        instanceId: instanceId,
                        appId: appId
                    });

                    // 检查设置类型和ID是否有效
                    if (!settingType) {
                        console.error('无法保存回复内容：设置类型为空');
                        showCustomAlert('设置类型无效，无法保存');
                        return;
                    }

                    if (settingType === 'instance' && !instanceId) {
                        console.error('无法保存回复内容：实例ID为空');
                        showCustomAlert('实例ID无效，无法保存');
                        return;
                    }

                    if (settingType === 'app' && !appId) {
                        console.error('无法保存回复内容：应用ID为空');
                        showCustomAlert('应用ID无效，无法保存');
                        return;
                    }

                    const shortcuts = modal.querySelectorAll('.edit-reply-shortcut');

                    if (shortcuts.length === 0) {
                        showCustomAlert('请至少添加一条回复内容');
                        return;
                    }

                    console.log(`正在保存回复内容: ${settingType === 'app' ? '应用ID ' + appId : '实例ID ' + instanceId}`);

                    try {
                        // 1. 获取所有回复内容
                        const replyContents = Array.from(shortcuts).map(shortcut => {
                            return shortcut.childNodes[0].textContent.trim();
                        });

                        // 2. 合并回复内容
                        const combinedContent = replyContents.join('【or】');
                        console.log('合并后的回复内容:', combinedContent);

                        // 3. 获取当前编辑的是转人工回复还是默认回复
                        const isTransferReply = modal.dataset.targetInput === 'transfer';
                        console.log('当前编辑的是转人工回复?', isTransferReply);

                        if (settingType === 'app') {
                            // 4. 创建应用设置的深拷贝
                            const updatedSettings = JSON.parse(JSON.stringify(window.appSettings[appId]));
                            console.log('更新前的应用设置:', updatedSettings);

                            // 5. 确保general对象存在
                            if (!updatedSettings.general) {
                                updatedSettings.general = {
                                    defaultReply: "当前消息有点多，我稍后再回复您【or】您好，有什么能帮助...",
                                    transferReply: "已为您转接其他客服"
                                };
                            }

                            // 6. 根据类型更新对应的回复内容
                            if (isTransferReply) {
                                updatedSettings.general.transferReply = combinedContent;
                                console.log('更新了应用转人工默认回复');
                            } else {
                                updatedSettings.general.defaultReply = combinedContent;
                                console.log('更新了应用默认回复');
                            }

                            // 7. 保存更新后的设置
                            window.appSettings[appId] = updatedSettings;
                            console.log('更新后的应用设置:', JSON.parse(JSON.stringify(window.appSettings[appId])));

                            // 8. 更新UI中对应的输入框
                            if (document.getElementById('settingsModal').dataset.currentAppId === appId) {
                                if (isTransferReply) {
                                    document.querySelectorAll('#generalSettings .settings-input')[1].value = combinedContent;
                                } else {
                                    document.querySelectorAll('#generalSettings .settings-input')[0].value = combinedContent;
                                }
                            }
                        } else if (settingType === 'instance') {
                            // 4. 创建实例设置的深拷贝
                            const updatedSettings = JSON.parse(JSON.stringify(window.instanceSettings[instanceId]));
                            console.log('更新前的实例设置:', updatedSettings);

                            // 5. 确保general对象存在
                            if (!updatedSettings.general) {
                                updatedSettings.general = {
                                    defaultReply: "当前消息有点多，我稍后再回复您【or】您好，有什么能帮助...",
                                    transferReply: "已为您转接其他客服"
                                };
                            }

                            // 6. 根据类型更新对应的回复内容
                            if (isTransferReply) {
                                updatedSettings.general.transferReply = combinedContent;
                                console.log('更新了实例转人工默认回复');
                            } else {
                                updatedSettings.general.defaultReply = combinedContent;
                                console.log('更新了实例默认回复');
                            }

                            // 7. 保存更新后的设置
                            window.instanceSettings[instanceId] = updatedSettings;
                            console.log('更新后的实例设置:', JSON.parse(JSON.stringify(window.instanceSettings[instanceId])));

                            // 8. 更新UI中对应的输入框
                            if (document.getElementById('settingsModal').dataset.currentInstanceId === instanceId) {
                                if (isTransferReply) {
                                    document.querySelectorAll('#generalSettings .settings-input')[1].value = combinedContent;
                                } else {
                                    document.querySelectorAll('#generalSettings .settings-input')[0].value = combinedContent;
                                }
                            }
                        }

                        // 9. 显示保存成功提示
                        showCustomAlert('保存成功');

                        // 10. 关闭编辑弹窗
                        modal.style.display = 'none';
                        document.getElementById('helpOverlay').classList.remove('show');

                        // 11. 清除ID引用，以避免数据混淆
                        modal.dataset.currentInstanceId = '';
                        modal.dataset.currentAppId = '';
                        modal.dataset.settingType = '';
                        modal.dataset.targetInput = '';

                    } catch (error) {
                        console.error('保存回复内容时发生错误:', error);
                        showCustomAlert('保存失败，请重试');
                    }
                });
            }
        });

        // 添加通用设置变更的监听
        document.querySelectorAll('#generalSettings .settings-input').forEach(input => {
            input.addEventListener('change', function() {
                // 获取当前编辑的实例ID
                const instanceId = document.getElementById('settingsModal').dataset.currentInstanceId;
                if (!instanceId) {
                    console.error('无法保存通用设置：实例ID无效');
                    return;
                }

                console.log(`通用设置输入框变更: 实例ID ${instanceId}`);
                console.log('保存前的实例设置:', JSON.parse(JSON.stringify(instanceSettings[instanceId])));

                // 获取当前的通用设置值
                const defaultReply = document.querySelectorAll('#generalSettings .settings-input')[0].value;
                const transferReply = document.querySelectorAll('#generalSettings .settings-input')[1].value;

                // 创建新的设置对象以避免引用问题
                const updatedSettings = JSON.parse(JSON.stringify(instanceSettings[instanceId]));

                // 确保general对象存在
                if (!updatedSettings.general) {
                    updatedSettings.general = {};
                }

                // 更新通用设置
                updatedSettings.general.defaultReply = defaultReply;
                updatedSettings.general.transferReply = transferReply;

                // 保存更新后的设置对象
                instanceSettings[instanceId] = updatedSettings;

                console.log('保存后的实例设置:', JSON.parse(JSON.stringify(instanceSettings[instanceId])));
                console.log('所有实例的设置数据:', Object.keys(instanceSettings).map(id => {
                    return {
                        id: id,
                        settings: JSON.parse(JSON.stringify(instanceSettings[id]))
                    };
                }));
            });
        });

        // "添加"按钮点击事件，用于添加编辑框中的内容到快捷回复列表
        document.addEventListener('DOMContentLoaded', function() {
            const editReplyAddBtn = document.querySelector('.edit-reply-add-btn');
            if (editReplyAddBtn) {
                // 移除所有现有的点击事件监听器，防止重复添加
                const newEditReplyAddBtn = editReplyAddBtn.cloneNode(true);
                editReplyAddBtn.parentNode.replaceChild(newEditReplyAddBtn, editReplyAddBtn);

                // 添加新的点击事件监听器
                newEditReplyAddBtn.addEventListener('click', function() {
                    const modal = document.getElementById('editReplyModal');
                    const instanceId = modal.dataset.currentInstanceId; // 从模态框获取实例ID
                    const appId = modal.dataset.currentAppId; // 从模态框获取应用ID
                    const settingType = modal.dataset.settingType; // 从模态框获取设置类型

                    console.log('添加按钮点击事件触发，当前数据:', {
                        settingType: settingType,
                        instanceId: instanceId,
                        appId: appId
                    });

                    // 检查设置类型和ID是否有效
                    if (settingType === 'instance' && (!instanceId || !window.instanceSettings[instanceId])) {
                        console.error('无法添加回复内容：实例ID无效', instanceId);
                        showCustomAlert('实例ID无效，无法添加回复内容');
                        return;
                    }

                    if (settingType === 'app' && (!appId || !window.appSettings[appId])) {
                        console.error('无法添加回复内容：应用ID无效', appId);
                        showCustomAlert('应用ID无效，无法添加回复内容');
                        return;
                    }

                    const textarea = modal.querySelector('.edit-reply-textarea');
                    const content = textarea.value.trim();

                    if (!content) {
                        showCustomAlert('请输入回复内容');
                        return;
                    }

                    console.log(`添加回复内容到${settingType === 'app' ? '应用 ' + appId : '实例 ' + instanceId} 的编辑列表:`, content);

                    // 创建快捷回复元素
                    const shortcut = document.createElement('div');
                    shortcut.className = 'edit-reply-shortcut';
                    shortcut.innerHTML = `
                        ${content}
                        <i class="fas fa-times delete-icon"></i>
                    `;

                    // 添加点击事件，将内容加载到编辑框
                    shortcut.addEventListener('click', function(e) {
                        // 如果点击的是删除图标，不执行加载操作
                        if (e.target.classList.contains('delete-icon')) {
                            return;
                        }

                        // 如果当前项已经在编辑状态，则取消编辑
                        if (this.classList.contains('editing')) {
                            this.classList.remove('editing');
                            textarea.value = '';
                            modal.querySelector('.edit-reply-counter').textContent = '0/800';
                            return;
                        }

                        // 否则进入编辑状态
                        textarea.value = content;
                        textarea.focus();
                        // 更新字数统计
                        modal.querySelector('.edit-reply-counter').textContent = `${content.length}/800`;
                        // 标记当前正在编辑的回复项
                        modal.querySelectorAll('.edit-reply-shortcut').forEach(item => item.classList.remove('editing'));
                        this.classList.add('editing');
                    });

                    // 添加删除事件
                    shortcut.querySelector('.delete-icon').addEventListener('click', function(e) {
                        e.stopPropagation();
                        shortcut.remove();
                    });

                    // 添加到容器
                    modal.querySelector('.edit-reply-shortcuts').appendChild(shortcut);

                    // 清空文本框
                    textarea.value = '';
                    // 重置计数器
                    modal.querySelector('.edit-reply-counter').textContent = '0/800';
                });
            }
        });

        // 初始化自定义悬浮提示
        function initCustomTooltip() {
            const tooltip = document.getElementById('customTooltip');
            if (!tooltip) return;

            let tooltipTimeout;

            // 添加悬浮提示事件
            document.addEventListener('mouseover', function(e) {
                const target = e.target;
                if (target.hasAttribute('data-tooltip') || target.closest('[data-tooltip]')) {
                    const element = target.hasAttribute('data-tooltip') ? target : target.closest('[data-tooltip]');
                    const tooltipText = element.getAttribute('data-tooltip');

                    if (tooltipText) {
                        // 清除之前的定时器
                        clearTimeout(tooltipTimeout);

                        // 设置提示文本
                        tooltip.textContent = tooltipText;

                        // 计算位置
                        const rect = element.getBoundingClientRect();

                        // 先设置内容，再计算宽度
                        tooltip.style.visibility = 'hidden';
                        tooltip.classList.add('show');

                        // 获取提示框尺寸
                        const tooltipWidth = tooltip.offsetWidth;
                        const tooltipHeight = tooltip.offsetHeight;

                        // 计算位置，确保提示框不会超出屏幕
                        let left = rect.left + (rect.width / 2) - (tooltipWidth / 2);
                        let top = rect.top - tooltipHeight - 10;

                        // 防止提示框超出屏幕左右边界
                        if (left < 10) left = 10;
                        if (left + tooltipWidth > window.innerWidth - 10) {
                            left = window.innerWidth - tooltipWidth - 10;
                        }

                        // 防止提示框超出屏幕上边界
                        if (top < 10) {
                            // 如果上方空间不足，显示在元素下方
                            top = rect.bottom + 10;
                            // 调整箭头位置
                            tooltip.classList.add('tooltip-bottom');
                        } else {
                            tooltip.classList.remove('tooltip-bottom');
                        }

                        // 设置位置
                        tooltip.style.left = left + 'px';
                        tooltip.style.top = top + 'px';

                        // 显示提示框
                        tooltip.style.visibility = 'visible';
                    }
                }
            });

            // 移除悬浮提示事件
            document.addEventListener('mouseout', function(e) {
                const target = e.target;
                if (target.hasAttribute('data-tooltip') || target.closest('[data-tooltip]')) {
                    // 设置延时隐藏，避免鼠标快速移动时闪烁
                    tooltipTimeout = setTimeout(() => {
                        tooltip.classList.remove('show');
                    }, 100);
                }
            });

            // 防止页面滚动时提示框位置不正确
            window.addEventListener('scroll', function() {
                tooltip.classList.remove('show');
            });

            // 防止窗口调整时提示框位置不正确
            window.addEventListener('resize', function() {
                tooltip.classList.remove('show');
            });
        }

        // 文档加载完成后执行初始化
        document.addEventListener('DOMContentLoaded', function () {
            console.log('DOM 加载完成，开始初始化');

            // 初始化自定义悬浮提示
            initCustomTooltip();

            // 确保顶部栏高度一致
            document.querySelector('.header').style.height = '60px';
            document.querySelectorAll('.home-btn').forEach(btn => {
                btn.style.height = '60px';
            });

            // 初始化抖店应用设置
            initAppSettings('douyin');
            console.log('初始化抖店应用设置完成');

            // 初始化页面切换功能
            initPageSwitcher();

            // 根据当前激活的页面初始化功能
            const activePage = document.querySelector('.page.active');
            if (activePage && activePage.id === 'keyword-page') {
                initKeywordPage();
                window.keywordPageInitialized = true;
            }

            // 其他初始化逻辑...
        });

        // 初始化页面切换功能
        function initPageSwitcher() {
            const navButtons = document.querySelectorAll('.home-btn');
            const container = document.querySelector('.container');

            navButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 如果已经是激活状态，不需要切换
                    if (this.classList.contains('active')) return;

                    // 获取目标页面ID
                    const targetPageId = this.getAttribute('data-page');

                    // 切换导航按钮激活状态
                    navButtons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // 切换页面显示
                    document.querySelectorAll('.page').forEach(page => {
                        page.classList.remove('active');
                    });
                    document.getElementById(targetPageId).classList.add('active');

                    // 确保顶部栏高度一致
                    document.querySelector('.header').style.height = '60px';

                    // 如果切换到关键词页面，初始化关键词功能
                    if (targetPageId === 'keyword-page') {
                        // 检查是否已经初始化
                        if (!window.keywordPageInitialized) {
                            console.log('关键词页面首次初始化');
                            initKeywordPage();
                            window.keywordPageInitialized = true;
                        } else {
                            console.log('关键词页面已初始化，刷新UI');
                            // 刷新UI
                            if (typeof initCategoryList === 'function') {
                                initCategoryList();
                                console.log('已刷新关键词分类列表');
                            }

                            if (typeof window.renderKeywordList === 'function') {
                                window.renderKeywordList();
                                console.log('已刷新关键词列表');
                            }

                            if (typeof updateKeywordCountAndList === 'function') {
                                updateKeywordCountAndList();
                                console.log('已刷新关键词总数和列表');
                            }
                        }
                    }

                    console.log(`切换到页面: ${targetPageId}`);
                });
            });
        }

        // 全局变量声明
        window.activeCategory = null; // 当前活动的分类ID
        // 使用全局的 window.keywordData
        window.keywordCategories = []; // 关键词分类数组
        window.renderKeywordList; // 声明渲染函数引用

        // 初始化关键词页面功能
        function initKeywordPage() {
            console.log('初始化关键词页面');

            console.log('检查关键词数据是否已加载:', window.keywordData, window.keywordCategories);

            // 如果没有从本地存储加载关键词数据，使用示例数据
            if (!window.keywordCategories || window.keywordCategories.length === 0) {
                console.log('没有找到已加载的关键词分类数据，使用示例数据');
                // 关键词分类数据
                window.keywordCategories = [
                    { id: 'cat1', name: '五代耳机', keyword: '耳机,airpods', sortOrder: 0 },
                    { id: 'cat2', name: '头戴式', keyword: '头戴,headphones', sortOrder: 1 }
                    // 更多分类可以在这里添加
                ];

                // 对分类进行排序
                window.keywordCategories.sort((a, b) => a.sortOrder - b.sortOrder);
            } else {
                console.log('使用已加载的关键词分类数据:', window.keywordCategories.length);
                // 对分类进行排序
                window.keywordCategories.sort((a, b) => a.sortOrder - b.sortOrder);
            }

            // 修复媒体URL格式的函数
            function fixMediaUrlFormat(mediaData) {
                if (!mediaData || !Array.isArray(mediaData)) return mediaData;

                return mediaData.map(media => {
                    if (!media || !media.url) return media;

                    // 创建一个新对象，避免修改原始对象
                    const newMedia = {...media};

                    // 强制设置 isBase64 属性，确保它存在
                    if (newMedia.type === 'image' || newMedia.type === 'video') {
                        if (newMedia.url.includes('base64') || !newMedia.url.startsWith('http')) {
                            newMedia.isBase64 = true;
                        }
                    }

                    // 如果是Base64格式，确保有正确的MIME类型前缀
                    if (newMedia.type === 'image' && !newMedia.url.startsWith('data:image/')) {
                        if (newMedia.url.includes('base64,')) {
                            const base64Content = newMedia.url.split('base64,')[1];
                            newMedia.url = `data:image/jpeg;base64,${base64Content}`;
                            newMedia.isBase64 = true;
                        } else if (!newMedia.url.startsWith('http')) {
                            try {
                                // 检查是否是有效的Base64字符串
                                const testDecode = atob(newMedia.url);
                                newMedia.url = `data:image/jpeg;base64,${newMedia.url}`;
                                newMedia.isBase64 = true;
                            } catch (e) {
                                console.error('无法将内容解析为Base64:', e);

                                // 如果解析失败，尝试将其作为纯文本处理
                                try {
                                    // 尝试将其作为图片数据处理
                                    newMedia.url = `data:image/jpeg;base64,${newMedia.url}`;
                                    newMedia.isBase64 = true;
                                    console.log('尝试将其作为图片数据处理');
                                } catch (textError) {
                                    console.error('尝试将其作为图片数据处理失败:', textError);
                                }
                            }
                        }
                        console.log(`已修正图片媒体的URL格式: ${newMedia.url.substring(0, 50)}`);
                    } else if (newMedia.type === 'video' && !newMedia.url.startsWith('data:video/')) {
                        if (newMedia.url.includes('base64,')) {
                            const base64Content = newMedia.url.split('base64,')[1];
                            newMedia.url = `data:video/mp4;base64,${base64Content}`;
                            newMedia.isBase64 = true;
                        } else if (!newMedia.url.startsWith('http')) {
                            try {
                                // 检查是否是有效的Base64字符串
                                const testDecode = atob(newMedia.url);
                                newMedia.url = `data:video/mp4;base64,${newMedia.url}`;
                                newMedia.isBase64 = true;
                            } catch (e) {
                                console.error('无法将内容解析为Base64:', e);

                                // 如果解析失败，尝试将其作为纯文本处理
                                try {
                                    // 尝试将其作为视频数据处理
                                    newMedia.url = `data:video/mp4;base64,${newMedia.url}`;
                                    newMedia.isBase64 = true;
                                    console.log('尝试将其作为视频数据处理');
                                } catch (textError) {
                                    console.error('尝试将其作为视频数据处理失败:', textError);
                                }
                            }
                        }
                        console.log(`已修正视频媒体的URL格式: ${newMedia.url.substring(0, 50)}`);
                    }

                    // 打印调试信息
                    console.log(`媒体类型: ${newMedia.type}, isBase64: ${newMedia.isBase64}, URL前缀: ${newMedia.url.substring(0, 50)}`);

                    return newMedia;
                });
            }

            // 如果没有关键词数据，初始化空对象
            if (!window.keywordData || Object.keys(window.keywordData).length === 0) {
                console.log('没有找到已加载的关键词数据，初始化空对象');
                window.keywordData = {};

                // 初始化示例关键词数据
                const exampleKeywords = {
                    'cat1': [
                        {
                            id: 'kw1',
                            keyword: '关键词编辑框示例',
                            reply: '回复内容编辑框示例',
                            media: [
                                { type: 'image', url: '' },
                                { type: 'image', url: '' }
                            ]
                        }
                        // 可以添加更多关键词
                    ],
                    'cat2': []
                };

                // 保存到本地存储
                if (typeof categoryStorageAdapter !== 'undefined') {
                    // 保存分类组
                    window.keywordCategories.forEach(category => {
                        categoryStorageAdapter.saveKeywordGroup(category.id, category);
                        console.log(`保存示例分类组: ${category.id}`);

                        // 初始化分类的关键词数组
                        window.keywordData[category.id] = exampleKeywords[category.id] || [];
                    });

                    // 保存关键词数据
                    Object.entries(exampleKeywords).forEach(([groupId, keywords]) => {
                        keywords.forEach(keyword => {
                            categoryStorageAdapter.saveKeywordReply(groupId, keyword.id, keyword);
                            console.log(`保存示例关键词: 分类ID=${groupId}, 关键词ID=${keyword.id}`);
                        });
                    });
                }
            } else {
                console.log('使用已加载的关键词数据:', Object.keys(window.keywordData));

                // 修复已加载的关键词数据中的媒体URL格式
                Object.keys(window.keywordData).forEach(categoryId => {
                    if (Array.isArray(window.keywordData[categoryId])) {
                        window.keywordData[categoryId] = window.keywordData[categoryId].map(keyword => {
                            if (keyword && keyword.media) {
                                // 修复媒体URL格式
                                keyword.media = fixMediaUrlFormat(keyword.media);
                                console.log(`已修复分类 ${categoryId} 中关键词 ${keyword.id} 的媒体URL格式`);
                            }
                            return keyword;
                        });
                    }
                });

                // 确保每个分类都有对应的关键词数组
                window.keywordCategories.forEach(category => {
                    if (!window.keywordData[category.id]) {
                        window.keywordData[category.id] = [];
                        console.log(`初始化分类 ${category.id} 的关键词数组`);
                    }
                });
            }

            // 当前活动的分类ID，初始为null表示未选中
            if (window.activeCategory === undefined) {
                window.activeCategory = null;
            }

            // 初始化关键词分类列表
            window.initCategoryList = function() {
                const categoryList = document.getElementById('keywordCategoryList');
                categoryList.innerHTML = '';

                console.log('初始化分类列表:', window.keywordCategories.length);
                window.keywordCategories.forEach(cat => {
                    const categoryItem = document.createElement('div');
                    categoryItem.className = `keyword-category-item ${cat.id === window.activeCategory ? 'active' : ''}`;
                    categoryItem.dataset.id = cat.id;
                    categoryItem.dataset.sortOrder = cat.sortOrder;

                    categoryItem.innerHTML = `
                        <div class="drag-handle"><i class="fas fa-grip-lines"></i></div>
                        <div class="keyword-category-name" data-tooltip="${cat.name}">${cat.name}</div>
                        <div class="keyword-category-sort">排序值: ${cat.sortOrder}</div>
                        <div class="keyword-category-delete">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    `;

                    // 点击分类切换活动状态
                    categoryItem.addEventListener('click', function(e) {
                        if (e.target.closest('.keyword-category-delete')) {
                            // 点击删除按钮
                            deleteCategory(cat.id);
                            return;
                        }

                        // 切换分类选中状态
                        if (window.activeCategory === cat.id) {
                            // 如果当前已选中，则取消选中
                            setActiveCategory(null);
                        } else {
                            // 否则选中该分类
                            setActiveCategory(cat.id);
                        }
                    });

                    // 添加双击事件处理
                    categoryItem.addEventListener('dblclick', function(e) {
                        // 如果点击的是删除按钮或拖动把手，不处理
                        if (e.target.closest('.keyword-category-delete') || e.target.closest('.drag-handle')) {
                            return;
                        }

                        // 显示编辑对话框
                        editCategory(cat.id);
                    });

                    categoryList.appendChild(categoryItem);
                });

                // 更新中间栏标题
                updateKeywordListTitle();

                // 初始化拖拽排序
                initSortable();
            }

            // 初始化拖拽排序功能
            function initSortable() {
                const categoryList = document.getElementById('keywordCategoryList');

                // 如果已经初始化过，先销毁
                if (categoryList.sortableInstance) {
                    categoryList.sortableInstance.destroy();
                }

                // 创建新的Sortable实例
                categoryList.sortableInstance = new Sortable(categoryList, {
                    animation: 150,
                    handle: '.drag-handle', // 通过拖动把手元素来拖动
                    ghostClass: 'sortable-ghost', // 拖动时元素的类名
                    chosenClass: 'sortable-chosen', // 被选中元素的类名
                    onEnd: function(evt) {
                        // 更新排序值
                        const items = Array.from(categoryList.querySelectorAll('.keyword-category-item'));
                        items.forEach((item, index) => {
                            const categoryId = item.dataset.id;
                            const category = window.keywordCategories.find(cat => cat.id === categoryId);
                            if (category) {
                                category.sortOrder = index;
                                item.dataset.sortOrder = index;
                                item.querySelector('.keyword-category-sort').textContent = `排序值: ${index}`;

                                // 保存到本地存储
                                if (typeof categoryStorageAdapter !== 'undefined') {
                                    categoryStorageAdapter.saveKeywordGroup(categoryId, category);
                                    console.log(`分类 ${categoryId} 的排序值已更新并保存到本地存储: ${index}`);
                                }
                            }
                        });

                        // 重新排序分类数据
                        window.keywordCategories.sort((a, b) => a.sortOrder - b.sortOrder);

                        console.log('更新后的分类排序:', window.keywordCategories.map(cat => ({id: cat.id, name: cat.name, sortOrder: cat.sortOrder})));
                    }
                });
            }

            // 添加新的关键词分类
            function addNewCategory() {
                // 显示自定义分类添加对话框
                const categoryPrompt = document.getElementById('customCategoryPrompt');
                const overlay = document.getElementById('dialogOverlay');
                const categoryNameInput = document.getElementById('categoryNameInput');
                const categoryKeywordInput = document.getElementById('categoryKeywordInput');
                const categorySortInput = document.getElementById('categorySortInput');
                const cancelButton = document.getElementById('categoryPromptCancel');
                const confirmButton = document.getElementById('categoryPromptConfirm');

                // 设置默认排序值为当前最大排序值+1
                const maxSortOrder = window.keywordCategories.length > 0
                    ? Math.max(...window.keywordCategories.map(cat => cat.sortOrder))
                    : -1;
                categorySortInput.value = maxSortOrder + 1;

                // 清空其他输入框
                categoryNameInput.value = '';
                categoryKeywordInput.value = '';

                // 显示对话框
                categoryPrompt.classList.add('show');
                overlay.classList.add('show');

                // 设置焦点
                setTimeout(() => categoryNameInput.focus(), 100);

                // 取消按钮事件
                cancelButton.onclick = function() {
                    categoryPrompt.classList.remove('show');
                    overlay.classList.remove('show');
                };

                // 确认按钮事件
                confirmButton.onclick = function() {
                    const categoryName = categoryNameInput.value.trim();
                    const categoryKeyword = categoryKeywordInput.value.trim();
                    const categorySortOrder = parseInt(categorySortInput.value) || 0;

                    if (categoryName === '') {
                        showCustomAlert('分类组名称不能为空！');
                        return;
                    }

                    // 创建新分类 - 使用深拷贝确保数据独立
                    const newCategoryId = 'cat' + new Date().getTime();
                    const newCategory = {
                        id: newCategoryId,
                        name: categoryName,
                        keyword: categoryKeyword,
                        sortOrder: categorySortOrder
                    };

                    // 添加到分类列表
                    window.keywordCategories.push(JSON.parse(JSON.stringify(newCategory)));

                    // 初始化该分类的关键词数据 - 使用空数组确保数据独立
                    window.keywordData[newCategoryId] = [];

                    // 按排序值重新排序
                    window.keywordCategories.sort((a, b) => a.sortOrder - b.sortOrder);

                    // 保存到本地存储
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        // 保存分类组
                        categoryStorageAdapter.saveKeywordGroup(newCategoryId, newCategory);
                        console.log(`新分类已保存到本地存储: 分类ID=${newCategoryId}`);
                    }

                    // 更新UI
                    initCategoryList();
                    setActiveCategory(newCategoryId);

                    // 关闭对话框
                    categoryPrompt.classList.remove('show');
                    overlay.classList.remove('show');

                    showCustomAlert('添加分类成功！');
                };
            }

            // 设置活动分类
            function setActiveCategory(categoryId) {
                console.log(`设置活动分类: ${categoryId}`);
                window.activeCategory = categoryId;

                // 更新UI
                document.querySelectorAll('.keyword-category-item').forEach(item => {
                    item.classList.toggle('active', item.dataset.id === categoryId);
                });

                // 更新中间栏标题
                updateKeywordListTitle();

                // 根据是否选中更新添加按钮状态
                const addKeywordBtn = document.getElementById('addKeywordBtn');
                if (window.activeCategory === null) {
                    addKeywordBtn.classList.add('disabled');
                    addKeywordBtn.style.pointerEvents = 'none';
                    addKeywordBtn.style.opacity = '0.5';
                } else {
                    addKeywordBtn.classList.remove('disabled');
                    addKeywordBtn.style.pointerEvents = 'auto';
                    addKeywordBtn.style.opacity = '1';
                }

                // 刷新关键词列表
                renderKeywordList();

                // 更新右侧关键词总数和列表
                updateKeywordCountAndList();
            }

            // 更新关键词列表标题
            function updateKeywordListTitle() {
                const title = document.querySelector('.keyword-middle-panel .keyword-panel-title');

                if (window.activeCategory === null) {
                    // 未选中任何分类
                    title.innerHTML = `<i class="fas fa-comment-dots"></i>【未选中】关键词回复列表`;
                } else {
                    // 已选中分类
                    const currentCategory = window.keywordCategories.find(cat => cat.id === window.activeCategory);
                    if (currentCategory) {
                        title.innerHTML = `<i class="fas fa-comment-dots"></i>【${currentCategory.name}】关键词回复列表`;
                    }
                }
            }

            // 编辑分类
            function editCategory(categoryId) {
                // 获取分类信息
                const category = window.keywordCategories.find(cat => cat.id === categoryId);
                if (!category) {
                    console.error('找不到分类:', categoryId);
                    return;
                }

                // 显示自定义分类添加对话框
                const categoryPrompt = document.getElementById('customCategoryPrompt');
                const overlay = document.getElementById('dialogOverlay');
                const categoryNameInput = document.getElementById('categoryNameInput');
                const categoryKeywordInput = document.getElementById('categoryKeywordInput');
                const categorySortInput = document.getElementById('categorySortInput');
                const cancelButton = document.getElementById('categoryPromptCancel');
                const confirmButton = document.getElementById('categoryPromptConfirm');
                const promptTitle = categoryPrompt.querySelector('.prompt-title');

                // 更改标题
                promptTitle.textContent = '编辑关键词分类组';

                // 填充当前分类数据
                categoryNameInput.value = category.name;
                categoryKeywordInput.value = category.keyword || '';
                categorySortInput.value = category.sortOrder;

                // 显示对话框
                categoryPrompt.classList.add('show');
                overlay.classList.add('show');

                // 设置焦点
                setTimeout(() => categoryNameInput.focus(), 100);

                // 取消按钮事件
                cancelButton.onclick = function() {
                    categoryPrompt.classList.remove('show');
                    overlay.classList.remove('show');
                    // 恢复标题
                    promptTitle.textContent = '添加关键词分类组';
                };

                // 确认按钮事件
                confirmButton.onclick = function() {
                    const categoryName = categoryNameInput.value.trim();
                    const categoryKeyword = categoryKeywordInput.value.trim();
                    const categorySortOrder = parseInt(categorySortInput.value) || 0;

                    if (categoryName === '') {
                        showCustomAlert('分类组名称不能为空！');
                        return;
                    }

                    // 更新分类数据
                    category.name = categoryName;
                    category.keyword = categoryKeyword;
                    category.sortOrder = categorySortOrder;

                    // 重新排序分类数据
                    window.keywordCategories.sort((a, b) => a.sortOrder - b.sortOrder);

                    // 保存到本地存储
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        categoryStorageAdapter.saveKeywordGroup(categoryId, category);
                        console.log(`分类 ${categoryId} 已更新并保存到本地存储`);
                    }

                    // 更新UI
                    initCategoryList();

                    // 如果当前分类是活动的，保持其活动状态
                    if (window.activeCategory === categoryId) {
                        setActiveCategory(categoryId);
                    }

                    // 关闭对话框
                    categoryPrompt.classList.remove('show');
                    overlay.classList.remove('show');
                    // 恢复标题
                    promptTitle.textContent = '添加关键词分类组';

                    showCustomAlert('分类组编辑成功！');
                };
            }

            // 删除关键词分类
            function deleteCategory(categoryId) {
                const categoryName = window.keywordCategories.find(cat => cat.id === categoryId)?.name;
                // 确认删除
                showCustomConfirm(`确定要删除"${categoryName}"分类吗？删除后无法恢复。`, function(confirmed) {
                    if (!confirmed) return;

                    // 删除分类
                    const index = window.keywordCategories.findIndex(cat => cat.id === categoryId);
                    if (index !== -1) {
                        window.keywordCategories.splice(index, 1);

                        // 删除相关的关键词数据
                        delete window.keywordData[categoryId];

                        // 从本地存储中删除
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            // 注意：这里我们需要使用一个特殊的方法来删除分类
                            // 由于我们没有直接的deleteKeywordGroup方法，我们可以通过保存空对象来实现
                            // 在实际应用中，我们应该添加一个专门的删除方法
                            console.log(`从本地存储中删除分类: 分类ID=${categoryId}`);
                            categoryStorageAdapter.saveKeywordGroup(categoryId, {});
                        }

                        // 如果删除的是当前活动分类，设置为未选中状态
                        if (categoryId === window.activeCategory) {
                            window.activeCategory = null;
                        }

                        // 更新UI
                        initCategoryList();
                        renderKeywordList();
                        updateKeywordCountAndList();

                        showCustomAlert('删除分类成功！');
                    }
                });
            }

            // 渲染关键词列表
            window.renderKeywordList = function() {
                const keywordReplyContent = document.querySelector('.keyword-reply-content');
                keywordReplyContent.innerHTML = '';

                console.log(`渲染关键词列表: 当前活动分类=${window.activeCategory}`);

                // 未选中分类组的情况
                if (window.activeCategory === null) {
                    const notSelectedState = document.createElement('div');
                    notSelectedState.className = 'keyword-reply-item';
                    notSelectedState.innerHTML = `
                        <div class="not-selected-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <div class="title">未选中关键词分类组</div>
                            <div class="subtitle">请先选中一个分类组后再执行操作</div>
                        </div>
                    `;
                    keywordReplyContent.appendChild(notSelectedState);
                    return;
                }

                const keywords = window.keywordData[window.activeCategory] || [];

                if (keywords.length === 0) {
                    // 显示空状态
                    const emptyState = document.createElement('div');
                    emptyState.className = 'keyword-reply-item';
                    emptyState.innerHTML = `
                        <div class="empty-state-message">
                            <i class="fas fa-comment-slash"></i>
                            <div class="title">暂无关键词回复</div>
                            <div class="subtitle">可点击右上角添加按钮，添加关键词回复</div>
                        </div>
                    `;
                    keywordReplyContent.appendChild(emptyState);
                    return;
                }

                // 显示关键词列表
                keywords.forEach((keyword, index) => {
                    const keywordItem = document.createElement('div');
                    keywordItem.className = 'keyword-reply-item';
                    keywordItem.dataset.id = keyword.id;

                    // 为每个关键词项创建临时编辑状态对象
                    keywordItem.dataset.tempState = 'initialized';
                    keywordItem.tempData = {
                        keyword: keyword.keyword,
                        reply: keyword.reply,
                        media: JSON.parse(JSON.stringify(keyword.media || []))
                    };

                    let mediaPreviewHTML = '';
                    if (keywordItem.tempData.media && keywordItem.tempData.media.length > 0) {
                        mediaPreviewHTML = `
                            <div class="media-preview-area">
                                ${keywordItem.tempData.media.map(media => {
                                    // 确保 URL 是有效的，如果是Base64字符串或已经是有效URL则直接使用
                                    const mediaUrl = media.url;

                                    if (media.type === 'image') {
                                        // 图片预览 - 使用与新上传相同的结构
                                        return `
                                            <div class="media-preview-item">
                                                <div class="media-preview-delete">
                                                    <i class="fas fa-times"></i>
                                                </div>
                                                <div class="image-placeholder" data-media-type="image" data-media-url="${mediaUrl}">
                                                    <div class="image-thumbnail">
                                                        <img src="${mediaUrl}" alt="${media.name || '图片'}">
                                                        <div class="image-overlay">
                                                            <i class="fas fa-search-plus"></i>
                                                        </div>
                                                    </div>
                                                    <div class="image-info">
                                                        <div class="image-name">${media.name || '图片'}</div>
                                                        ${media.fileSize ? `<div class="image-size">${(media.fileSize / (1024 * 1024)).toFixed(2)} MB</div>` : ''}
                                                    </div>
                                                </div>
                                                <div class="media-preview-name">${media.name || '图片'}</div>
                                            </div>
                                        `;
                                    } else {
                                        // 视频预览 - 使用与新上传相同的结构
                                        return `
                                            <div class="media-preview-item">
                                                <div class="media-preview-delete">
                                                    <i class="fas fa-times"></i>
                                                </div>
                                                <div class="video-placeholder" data-media-type="video" data-media-url="${mediaUrl}">
                                                    <div class="video-thumbnail">
                                                        <video src="${mediaUrl}" preload="metadata"></video>
                                                        <div class="video-overlay">
                                                            <i class="fas fa-play-circle"></i>
                                                        </div>
                                                    </div>
                                                    <div class="video-info">
                                                        <div class="video-name">${media.name || '视频'}</div>
                                                        ${media.fileSize ? `<div class="video-size">${(media.fileSize / (1024 * 1024)).toFixed(2)} MB</div>` : ''}
                                                    </div>
                                                </div>
                                                <div class="media-preview-name">${media.name || '视频'}</div>
                                            </div>
                                        `;
                                    }
                                }).join('')}
                            </div>
                        `;
                    } else {
                        mediaPreviewHTML = `
                            <div class="media-preview-area empty-preview">
                                <div class="media-preview-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <div>暂无媒体文件，可点击下方按钮上传</div>
                                </div>
                            </div>
                        `;
                    }

                    keywordItem.innerHTML = `
                        <div class="keyword-reply-form">
                            <div class="keyword-form-group">
                                <div class="keyword-form-label">关键词:</div>
                                <input type="text" class="keyword-form-input" value="${keywordItem.tempData.keyword}" placeholder="关键词编辑框">
                            </div>
                            <div class="keyword-form-group">
                                <div class="keyword-form-label">回复:</div>
                                <input type="text" class="keyword-form-input" value="${keywordItem.tempData.reply}" placeholder="回复内容编辑框">
                            </div>

                            ${mediaPreviewHTML}

                            <div class="media-upload-buttons">
                                <button class="media-upload-btn">
                                    <i class="fas fa-image"></i>上传图片
                                </button>
                                <button class="media-upload-btn">
                                    <i class="fas fa-video"></i>上传视频
                                </button>
                                <button class="keyword-save-btn" data-id="${keyword.id}">
                                    <i class="fas fa-save"></i>保存
                                </button>
                                <button class="media-delete-btn" data-id="${keyword.id}">
                                    <i class="fas fa-trash-alt"></i>删除
                                </button>
                            </div>
                        </div>
                    `;

                    // 添加删除按钮事件
                    keywordItem.querySelector('.media-delete-btn').addEventListener('click', function() {
                        deleteKeyword(this.dataset.id);
                    });

                    // 添加保存按钮事件
                    keywordItem.querySelector('.keyword-save-btn').addEventListener('click', function() {
                        saveKeyword(this.dataset.id, keywordItem);
                    });

                    // 添加输入框值改变事件
                    const inputs = keywordItem.querySelectorAll('.keyword-form-input');
                    inputs.forEach((input, i) => {
                        input.addEventListener('input', function() {
                            // 更新临时数据但不提交到实际数据
                            if (i === 0) keywordItem.tempData.keyword = this.value;
                            if (i === 1) keywordItem.tempData.reply = this.value;

                            // 标记编辑框已被修改
                            keywordItem.dataset.tempState = 'modified';

                            // 高亮显示保存按钮，提示用户保存
                            const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                            saveBtn.classList.add('highlight');
                        });
                    });

                    // 添加媒体预览删除按钮事件
                    const deleteButtons = keywordItem.querySelectorAll('.media-preview-delete');
                    deleteButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const mediaItem = this.closest('.media-preview-item');
                            const index = Array.from(mediaItem.parentNode.children).indexOf(mediaItem);

                            // 删除临时媒体项，但不修改实际数据
                            if (keywordItem.tempData.media && index >= 0 && index < keywordItem.tempData.media.length) {
                                keywordItem.tempData.media = keywordItem.tempData.media.filter((_, i) => i !== index);
                                mediaItem.remove();

                                // 如果没有媒体了，显示空状态
                                if (keywordItem.tempData.media.length === 0) {
                                    updateMediaPreview(keywordItem);
                                }

                                // 标记编辑框已被修改
                                keywordItem.dataset.tempState = 'modified';

                                // 高亮显示保存按钮，提示用户保存
                                const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                                if (saveBtn) {
                                    saveBtn.classList.add('highlight');
                                }

                                showCustomAlert('媒体已临时删除，点击保存按钮后生效！');
                            }
                        });
                    });

                    // 添加上传按钮事件
                    const uploadButtons = keywordItem.querySelectorAll('.media-upload-btn');
                    uploadButtons.forEach((btn, index) => {
                        btn.addEventListener('click', function() {
                            // 模拟上传文件
                            simulateMediaUpload(keywordItem, index === 0 ? 'image' : 'video');
                        });
                    });

                    // 添加图片和视频点击放大预览事件
                    const mediaItems = keywordItem.querySelectorAll('.media-preview-item');
                    mediaItems.forEach(item => {
                        // 为整个媒体项添加点击事件，而不仅仅是媒体元素
                        item.addEventListener('click', function(e) {
                            // 阻止删除按钮的点击事件冒泡
                            if (e.target.closest('.media-preview-delete')) {
                                return;
                            }

                            e.stopPropagation(); // 阻止事件冒泡

                            // 获取媒体元素（图片或视频占位符）
                            const mediaElement = item.querySelector('.image-placeholder, .video-placeholder');
                            if (!mediaElement) return;

                            const mediaType = mediaElement.dataset.mediaType;
                            const mediaUrl = mediaElement.dataset.mediaUrl;

                            if (!mediaType || !mediaUrl) {
                                console.error('无法获取媒体信息');
                                return;
                            }

                            // 获取模态框元素
                            const modal = document.getElementById('mediaPreviewModal');
                            const modalBody = document.getElementById('mediaPreviewModalBody');

                            // 清空模态框内容
                            modalBody.innerHTML = '';

                            // 根据媒体类型创建对应元素
                            if (mediaType === 'image') {
                                // 清空模态框内容
                                modalBody.innerHTML = '';

                                // 添加加载中提示
                                const loadingElement = document.createElement('div');
                                loadingElement.className = 'image-loading';
                                loadingElement.innerHTML = `
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在准备图片预览...</div>
                                `;
                                modalBody.appendChild(loadingElement);

                                // 添加下载按钮
                                const fileName = `image_${Date.now()}.jpg`;
                                const downloadContainer = document.createElement('div');
                                downloadContainer.className = 'image-download-option';
                                downloadContainer.innerHTML = `
                                    <a href="${mediaUrl}" download="${fileName}" class="image-download-button">
                                        <i class="fas fa-download"></i> 下载图片
                                    </a>
                                `;
                                modalBody.appendChild(downloadContainer);

                                // 使用主进程创建临时文件并返回文件URL
                                if (window.electronAPI && typeof window.electronAPI.saveBase64ToTemp === 'function') {
                                    console.log('图片预览: 开始调用saveBase64ToTemp函数');
                                    console.log('图片URL前缀:', mediaUrl.substring(0, 50));

                                    // 调用主进程保存临时文件
                                    // 确保Base64格式正确
                                    let processedMediaUrl = mediaUrl;
                                    if (!processedMediaUrl.startsWith('data:image/')) {
                                        console.log('图片URL格式不正确，尝试修正...');
                                        // 尝试修正格式
                                        if (processedMediaUrl.includes('base64,')) {
                                            const base64Content = processedMediaUrl.split('base64,')[1];
                                            processedMediaUrl = `data:image/jpeg;base64,${base64Content}`;
                                        } else if (processedMediaUrl.startsWith('http')) {
                                            // 如果是URL，不进行处理，直接返回
                                            console.log('检测到URL格式，不进行处理');
                                            // 在这种情况下，我们应该使用原始图片而不是创建临时文件
                                            // 直接创建图片元素
                                            const imageContainer = document.createElement('div');
                                            imageContainer.className = 'image-preview-container';

                                            // 创建图片元素
                                            const imageElement = document.createElement('img');
                                            imageElement.className = 'image-preview-element';
                                            imageElement.src = processedMediaUrl;
                                            imageElement.alt = '放大图片';

                                            // 将图片元素添加到容器中
                                            imageContainer.appendChild(imageElement);

                                            // 创建下载按钮容器
                                            const downloadButtonContainer = document.createElement('div');
                                            downloadButtonContainer.className = 'image-controls-container';

                                            // 复制下载按钮到新容器
                                            const downloadBtn = downloadContainer.querySelector('.image-download-button');
                                            if (downloadBtn) {
                                                const newDownloadBtn = downloadBtn.cloneNode(true);
                                                downloadButtonContainer.appendChild(newDownloadBtn);

                                                // 添加下载按钮点击事件
                                                newDownloadBtn.addEventListener('click', function() {
                                                    console.log('开始下载图片:', fileName);
                                                    // 延迟关闭模态框，给用户一些时间看到下载开始
                                                    setTimeout(function() {
                                                        showCustomAlert('图片下载已开始，请查看您的下载文件夹');
                                                    }, 500);
                                                });
                                            }

                                            // 将下载按钮容器添加到图片容器
                                            imageContainer.appendChild(downloadButtonContainer);

                                            // 添加图片容器到模态框
                                            modalBody.innerHTML = '';
                                            modalBody.appendChild(imageContainer);

                                            // 移除原来的下载容器
                                            if (downloadContainer.parentNode) {
                                                downloadContainer.parentNode.removeChild(downloadContainer);
                                            }

                                            return;
                                        } else {
                                            // 尝试将其作为Base64内容处理
                                            try {
                                                // 检查是否是有效的Base64字符串
                                                const testDecode = atob(processedMediaUrl);
                                                processedMediaUrl = `data:image/jpeg;base64,${processedMediaUrl}`;
                                            } catch (e) {
                                                console.error('无法将内容解析为Base64:', e);

                                                // 如果解析失败，尝试将其作为纯文本处理
                                                try {
                                                    // 尝试将其作为图片数据处理
                                                    processedMediaUrl = `data:image/jpeg;base64,${processedMediaUrl}`;
                                                    console.log('尝试将其作为图片数据处理');
                                                } catch (textError) {
                                                    console.error('尝试将其作为图片数据处理失败:', textError);

                                                    // 如果所有尝试都失败，直接使用原始 URL
                                                    processedMediaUrl = mediaUrl;
                                                }
                                            }
                                        }
                                        console.log('修正后的图片URL前缀:', processedMediaUrl.substring(0, 50));
                                    }

                                    window.electronAPI.saveBase64ToTemp(processedMediaUrl).then(fileUrl => {
                                        if (fileUrl) {
                                            console.log('创建临时文件成功:', fileUrl);

                                            // 移除加载中提示
                                            const loadingEl = modalBody.querySelector('.image-loading');
                                            if (loadingEl) loadingEl.remove();

                                            // 创建图片容器
                                            const imageContainer = document.createElement('div');
                                            imageContainer.className = 'image-preview-container';

                                            // 创建图片元素
                                            const imageElement = document.createElement('img');
                                            imageElement.className = 'image-preview-element';
                                            imageElement.src = fileUrl;
                                            imageElement.alt = '放大图片';

                                            // 将图片元素添加到容器中
                                            imageContainer.appendChild(imageElement);

                                            // 创建下载按钮容器
                                            const downloadButtonContainer = document.createElement('div');
                                            downloadButtonContainer.className = 'image-controls-container';

                                            // 复制下载按钮到新容器
                                            const downloadBtn = downloadContainer.querySelector('.image-download-button');
                                            if (downloadBtn) {
                                                const newDownloadBtn = downloadBtn.cloneNode(true);
                                                downloadButtonContainer.appendChild(newDownloadBtn);

                                                // 添加下载按钮点击事件
                                                newDownloadBtn.addEventListener('click', function() {
                                                    console.log('开始下载图片:', fileName);
                                                    // 延迟关闭模态框，给用户一些时间看到下载开始
                                                    setTimeout(function() {
                                                        showCustomAlert('图片下载已开始，请查看您的下载文件夹');
                                                    }, 500);
                                                });
                                            }

                                            // 将下载按钮容器添加到图片容器
                                            imageContainer.appendChild(downloadButtonContainer);

                                            // 添加图片容器到模态框
                                            modalBody.innerHTML = '';
                                            modalBody.appendChild(imageContainer);

                                            // 移除原来的下载容器
                                            if (downloadContainer.parentNode) {
                                                downloadContainer.parentNode.removeChild(downloadContainer);
                                            }

                                            // 添加清理函数，在模态框关闭时删除临时文件
                                            const closeBtn = modal.querySelector('.media-preview-modal-close');
                                            if (closeBtn) {
                                                const originalClickHandler = closeBtn.onclick;
                                                closeBtn.onclick = function() {
                                                    // 删除临时文件
                                                    window.electronAPI.deleteTempFile(fileUrl).then(success => {
                                                        if (success) {
                                                            console.log('已删除临时文件:', fileUrl);
                                                        }
                                                    });

                                                    // 调用原始处理程序
                                                    if (typeof originalClickHandler === 'function') {
                                                        originalClickHandler();
                                                    } else {
                                                        modal.classList.remove('show');
                                                    }
                                                };
                                            }
                                        } else {
                                            // 移除加载中提示
                                            const loadingEl = modalBody.querySelector('.image-loading');
                                            if (loadingEl) loadingEl.remove();

                                            // 显示错误信息
                                            const errorElement = document.createElement('div');
                                            errorElement.className = 'image-error-message';
                                            errorElement.innerHTML = `
                                                <p>图片预览失败，请使用下方下载按钮下载后查看</p>
                                                <div class="error-details">错误信息: 无法创建临时文件</div>
                                            `;
                                            modalBody.insertBefore(errorElement, downloadContainer);
                                        }
                                    }).catch(error => {
                                        console.error('创建临时文件错误:', error);

                                        // 移除加载中提示
                                        const loadingEl = modalBody.querySelector('.image-loading');
                                        if (loadingEl) loadingEl.remove();

                                        // 显示错误信息
                                        const errorElement = document.createElement('div');
                                        errorElement.className = 'image-error-message';
                                        errorElement.innerHTML = `
                                            <p>图片预览失败，请使用下方下载按钮下载后查看</p>
                                            <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                        `;
                                        modalBody.insertBefore(errorElement, downloadContainer);
                                    });
                                } else {
                                    // 移除加载中提示
                                    const loadingEl = modalBody.querySelector('.image-loading');
                                    if (loadingEl) loadingEl.remove();

                                    // 创建一个优雅的图片信息和下载界面
                                    const imageContainer = document.createElement('div');
                                    imageContainer.className = 'image-preview-container';

                                    // 创建图片元素
                                    const imageElement = document.createElement('img');
                                    imageElement.className = 'image-preview-element';
                                    imageElement.src = mediaUrl;
                                    imageElement.alt = '放大图片';

                                    // 将图片元素添加到容器中
                                    imageContainer.appendChild(imageElement);

                                    // 创建下载按钮容器
                                    const downloadButtonContainer = document.createElement('div');
                                    downloadButtonContainer.className = 'image-controls-container';
                                    downloadButtonContainer.innerHTML = `
                                        <a href="${mediaUrl}" download="${fileName}" class="image-download-button">
                                            <i class="fas fa-download"></i> 下载图片
                                        </a>
                                    `;

                                    // 将下载按钮容器添加到图片容器
                                    imageContainer.appendChild(downloadButtonContainer);

                                    // 添加图片容器到模态框
                                    modalBody.innerHTML = '';
                                    modalBody.appendChild(imageContainer);

                                    // 添加下载按钮点击事件
                                    const downloadBtn = downloadButtonContainer.querySelector('.image-download-button');
                                    if (downloadBtn) {
                                        downloadBtn.addEventListener('click', function() {
                                            console.log('开始下载图片:', fileName);
                                            // 延迟关闭模态框，给用户一些时间看到下载开始
                                            setTimeout(function() {
                                                showCustomAlert('图片下载已开始，请查看您的下载文件夹');
                                            }, 500);
                                        });
                                    }
                                }
                            } else if (mediaType === 'video') {
                                try {
                                    // 清空模态框内容
                                    modalBody.innerHTML = '';

                                    // 添加加载中提示
                                    const loadingElement = document.createElement('div');
                                    loadingElement.className = 'video-loading';
                                    loadingElement.innerHTML = `
                                        <div class="loading-spinner"></div>
                                        <div class="loading-text">正在准备视频预览...</div>
                                    `;
                                    modalBody.appendChild(loadingElement);

                                    // 添加下载按钮
                                    const fileName = `video_${Date.now()}.mp4`;
                                    const downloadContainer = document.createElement('div');
                                    downloadContainer.className = 'video-download-option';
                                    downloadContainer.innerHTML = `
                                        <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                            <i class="fas fa-download"></i> 下载视频
                                        </a>
                                    `;
                                    modalBody.appendChild(downloadContainer);

                                    // 使用主进程创建临时文件并返回文件URL
                                    if (window.electronAPI && typeof window.electronAPI.saveBase64ToTemp === 'function') {
                                        // 调用主进程保存临时文件
                                        window.electronAPI.saveBase64ToTemp(mediaUrl).then(fileUrl => {
                                            if (fileUrl) {
                                                console.log('创建临时文件成功:', fileUrl);

                                                // 移除加载中提示
                                                const loadingEl = modalBody.querySelector('.video-loading');
                                                if (loadingEl) loadingEl.remove();

                                                // 创建视频容器
                                                const videoContainer = document.createElement('div');
                                                videoContainer.className = 'video-preview-container';

                                                // 创建视频元素
                                                const videoElement = document.createElement('video');
                                                videoElement.className = 'video-preview-element';
                                                videoElement.controls = true;
                                                videoElement.autoplay = true;

                                                // 将视频元素添加到容器中
                                                videoContainer.appendChild(videoElement);

                                                // 添加事件监听器
                                                videoElement.addEventListener('loadeddata', function() {
                                                    console.log('视频数据已加载');
                                                });

                                                videoElement.addEventListener('error', function(e) {
                                                    const errorCode = e.target.error ? e.target.error.code : 'unknown';
                                                    const errorMessage = e.target.error ? e.target.error.message : 'unknown error';
                                                    console.error('视频加载错误:', e);
                                                    console.error('错误代码:', errorCode);
                                                    console.error('错误信息:', errorMessage);

                                                    // 显示错误信息
                                                    videoElement.style.display = 'none';
                                                    const errorElement = document.createElement('div');
                                                    errorElement.className = 'video-error-message';
                                                    errorElement.innerHTML = `
                                                        <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                                        <div class="error-details">错误信息: ${errorMessage || '未知错误'}</div>
                                                        <div class="error-details">错误代码: ${errorCode}</div>
                                                    `;
                                                    modalBody.insertBefore(errorElement, downloadContainer);
                                                });

                                                // 设置视频源
                                                videoElement.src = fileUrl;

                                                // 创建下载按钮容器
                                                const downloadButtonContainer = document.createElement('div');
                                                downloadButtonContainer.className = 'video-controls-container';

                                                // 复制下载按钮到新容器
                                                const downloadBtn = downloadContainer.querySelector('.video-download-button');
                                                if (downloadBtn) {
                                                    const newDownloadBtn = downloadBtn.cloneNode(true);
                                                    downloadButtonContainer.appendChild(newDownloadBtn);

                                                    // 添加下载按钮点击事件
                                                    newDownloadBtn.addEventListener('click', function() {
                                                        console.log('开始下载视频');
                                                        // 延迟关闭模态框，给用户一些时间看到下载开始
                                                        setTimeout(function() {
                                                            showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                                        }, 500);
                                                    });
                                                }

                                                // 将下载按钮容器添加到视频容器
                                                videoContainer.appendChild(downloadButtonContainer);

                                                // 添加视频容器到模态框
                                                modalBody.innerHTML = '';
                                                modalBody.appendChild(videoContainer);

                                                // 移除原来的下载容器
                                                if (downloadContainer.parentNode) {
                                                    downloadContainer.parentNode.removeChild(downloadContainer);
                                                }

                                                // 添加清理函数，在模态框关闭时删除临时文件
                                                const closeBtn = modal.querySelector('.media-preview-modal-close');
                                                if (closeBtn) {
                                                    const originalClickHandler = closeBtn.onclick;
                                                    closeBtn.onclick = function() {
                                                        // 删除临时文件
                                                        window.electronAPI.deleteTempFile(fileUrl).then(success => {
                                                            if (success) {
                                                                console.log('已删除临时文件:', fileUrl);
                                                            }
                                                        });

                                                        // 调用原始处理程序
                                                        if (typeof originalClickHandler === 'function') {
                                                            originalClickHandler();
                                                        } else {
                                                            modal.classList.remove('show');
                                                        }
                                                    };
                                                }
                                            } else {
                                                // 移除加载中提示
                                                const loadingEl = modalBody.querySelector('.video-loading');
                                                if (loadingEl) loadingEl.remove();

                                                // 显示错误信息
                                                const errorElement = document.createElement('div');
                                                errorElement.className = 'video-error-message';
                                                errorElement.innerHTML = `
                                                    <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                                    <div class="error-details">错误信息: 无法创建临时文件</div>
                                                `;
                                                modalBody.insertBefore(errorElement, downloadContainer);
                                            }
                                        }).catch(error => {
                                            console.error('创建临时文件错误:', error);

                                            // 移除加载中提示
                                            const loadingEl = modalBody.querySelector('.video-loading');
                                            if (loadingEl) loadingEl.remove();

                                            // 显示错误信息
                                            const errorElement = document.createElement('div');
                                            errorElement.className = 'video-error-message';
                                            errorElement.innerHTML = `
                                                <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                                <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                            `;
                                            modalBody.insertBefore(errorElement, downloadContainer);
                                        });
                                    } else {
                                        // 移除加载中提示
                                        const loadingEl = modalBody.querySelector('.video-loading');
                                        if (loadingEl) loadingEl.remove();

                                        // 创建一个优雅的视频信息和下载界面
                                        const fileName = `video_${Date.now()}.mp4`;

                                        // 创建视频容器
                                        const videoContainer = document.createElement('div');
                                        videoContainer.className = 'video-preview-container';

                                        // 添加视频信息
                                        const videoInfo = document.createElement('div');
                                        videoInfo.className = 'video-info-container';
                                        videoInfo.innerHTML = `
                                            <div class="video-icon-large">
                                                <i class="fas fa-file-video"></i>
                                            </div>
                                            <div class="video-info-text">
                                                <h3>视频文件</h3>
                                                <p>由于Electron API不可用，无法直接预览视频。</p>
                                                <p>请使用下方按钮下载视频后在本地播放器中查看。</p>
                                            </div>
                                        `;
                                        videoContainer.appendChild(videoInfo);

                                        // 创建下载按钮容器
                                        const downloadButtonContainer = document.createElement('div');
                                        downloadButtonContainer.className = 'video-controls-container';
                                        downloadButtonContainer.innerHTML = `
                                            <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                                <i class="fas fa-download"></i> 下载视频
                                            </a>
                                        `;
                                        videoContainer.appendChild(downloadButtonContainer);

                                        // 添加视频容器到模态框
                                        modalBody.innerHTML = '';
                                        modalBody.appendChild(videoContainer);

                                        // 添加下载按钮点击事件
                                        const downloadBtn = downloadButtonContainer.querySelector('.video-download-button');
                                        if (downloadBtn) {
                                            downloadBtn.addEventListener('click', function() {
                                                console.log('开始下载视频:', fileName);
                                                // 延迟关闭模态框，给用户一些时间看到下载开始
                                                setTimeout(function() {
                                                    showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                                }, 500);
                                            });
                                        }

                                        // 移除原来的下载容器
                                        if (downloadContainer.parentNode) {
                                            downloadContainer.parentNode.removeChild(downloadContainer);
                                        }
                                    }

                                    // 添加下载按钮点击事件
                                    const downloadBtn = modalBody.querySelector('.video-download-button');
                                    if (downloadBtn) {
                                        downloadBtn.addEventListener('click', function() {
                                            console.log('开始下载视频:', fileName);
                                            // 延迟关闭模态框，给用户一些时间看到下载开始
                                            setTimeout(function() {
                                                showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                            }, 500);
                                        });
                                    }
                                } catch (error) {
                                    console.error('创建视频预览失败:', error);

                                    // 如果出错，显示下载选项
                                    const fileName = `video_${Date.now()}.mp4`;

                                    // 创建视频容器
                                    const videoContainer = document.createElement('div');
                                    videoContainer.className = 'video-preview-container';

                                    // 添加错误信息
                                    const errorInfo = document.createElement('div');
                                    errorInfo.className = 'video-error-message';
                                    errorInfo.innerHTML = `
                                        <p>视频预览失败，请使用下方按钮下载后查看</p>
                                        <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                    `;
                                    videoContainer.appendChild(errorInfo);

                                    // 创建下载按钮容器
                                    const downloadButtonContainer = document.createElement('div');
                                    downloadButtonContainer.className = 'video-controls-container';
                                    downloadButtonContainer.innerHTML = `
                                        <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                            <i class="fas fa-download"></i> 下载视频
                                        </a>
                                    `;
                                    videoContainer.appendChild(downloadButtonContainer);

                                    // 添加视频容器到模态框
                                    modalBody.innerHTML = '';
                                    modalBody.appendChild(videoContainer);

                                    // 添加下载按钮点击事件
                                    const downloadBtn = downloadButtonContainer.querySelector('.video-download-button');
                                    if (downloadBtn) {
                                        downloadBtn.addEventListener('click', function() {
                                            console.log('开始下载视频:', fileName);
                                            // 延迟关闭模态框，给用户一些时间看到下载开始
                                            setTimeout(function() {
                                                showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                            }, 500);
                                        });
                                    }
                                }
                            }

                            // 显示模态框
                            modal.classList.add('show');

                            // 添加关闭按钮事件
                            const closeBtn = modal.querySelector('.media-preview-modal-close');
                            closeBtn.addEventListener('click', function() {
                                modal.classList.remove('show');
                            });

                            // 点击模态框背景关闭
                            modal.addEventListener('click', function(e) {
                                if (e.target === modal) {
                                    modal.classList.remove('show');
                                }
                            });
                        });
                    });

                    keywordReplyContent.appendChild(keywordItem);
                });
            };

            // 模拟媒体上传
            function simulateMediaUpload(keywordItem, type) {
                console.log(`开始上传${type}...`);

                if (!keywordItem) {
                    console.error('上传失败：未找到关键词元素');
                    showCustomAlert('上传失败：未找到关键词元素');
                    return;
                }

                // 保存当前编辑框的值
                const inputs = keywordItem.querySelectorAll('.keyword-form-input');
                const currentKeyword = inputs[0].value;
                const currentReply = inputs[1].value;

                // 模拟文件选择对话框
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = type === 'image' ? 'image/*' : 'video/*';

                // 文件选择后处理
                input.onchange = function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const file = e.target.files[0];
                        console.log(`选择了文件: ${file.name}, 大小: ${file.size} 字节`);

                        // 模拟上传中状态
                        showCustomAlert(`正在上传${type === 'image' ? '图片' : '视频'}...`);

                        // 将文件转换为Base64字符串
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            try {
                                // 获取文件数据
                                const fileData = event.target.result;

                                // 在实际应用中，这里会有文件上传逻辑
                                // 现在我们模拟添加一个新的媒体项
                                if (!keywordItem.tempData.media) {
                                    keywordItem.tempData.media = [];
                                }

                                // 所有媒体类型都使用Base64编码保存
                                let newMedia = {
                                    type: type,
                                    url: fileData, // 保存Base64编码的内容
                                    name: file.name,
                                    fileSize: file.size,
                                    fileType: file.type,
                                    isBase64: true
                                };

                                // 确保Base64格式正确
                                if (type === 'image' && !newMedia.url.startsWith('data:image/')) {
                                    // 添加正确的MIME类型前缀
                                    const mimeType = file.type || 'image/jpeg';
                                    newMedia.url = `data:${mimeType};base64,${newMedia.url.split(',')[1] || newMedia.url}`;
                                    console.log('已修正图片Base64格式:', newMedia.url.substring(0, 50));
                                } else if (type === 'video' && !newMedia.url.startsWith('data:video/')) {
                                    // 添加正确的MIME类型前缀
                                    const mimeType = file.type || 'video/mp4';
                                    newMedia.url = `data:${mimeType};base64,${newMedia.url.split(',')[1] || newMedia.url}`;
                                    console.log('已修正视频Base64格式:', newMedia.url.substring(0, 50));
                                }

                                // 打印调试信息
                                console.log(`上传的${type}文件大小: ${file.size} 字节`);
                                console.log(`Base64编码长度: ${fileData.length} 字符`);
                                console.log(`文件类型: ${file.type}`);

                                // 如果是视频，添加额外标记
                                if (type === 'video') {
                                    newMedia.isVideo = true;
                                }

                                // 添加到临时媒体数组
                                keywordItem.tempData.media = [...keywordItem.tempData.media, newMedia];

                                // 标记已修改
                                keywordItem.dataset.tempState = 'modified';

                                // 仅更新媒体预览区域，不重新渲染整个列表
                                updateMediaPreview(keywordItem);

                                // 恢复编辑框的值
                                const updatedInputs = keywordItem.querySelectorAll('.keyword-form-input');
                                if (updatedInputs.length >= 2) {
                                    updatedInputs[0].value = currentKeyword;
                                    updatedInputs[1].value = currentReply;
                                }

                                // 高亮显示保存按钮，提示用户保存
                                const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                                if (saveBtn) {
                                    saveBtn.classList.add('highlight');
                                }

                                // 提示上传成功
                                showCustomAlert(`${type === 'image' ? '图片' : '视频'}已临时上传，点击保存按钮后生效！`);
                            } catch (error) {
                                console.error('上传处理失败：', error);
                                showCustomAlert('上传失败：处理文件时出错');
                            }
                        };

                        reader.onerror = function() {
                            console.error('读取文件失败');
                            showCustomAlert('上传失败：读取文件时出错');
                        };

                        // 所有文件类型都读取为Base64
                        reader.readAsDataURL(file);
                    }
                };

                // 触发文件选择对话框
                input.click();
            }

            // 只更新媒体预览区域而不重新渲染整个关键词列表
            function updateMediaPreview(keywordItem) {
                if (!keywordItem) return;

                const mediaPreviewArea = keywordItem.querySelector('.media-preview-area');
                if (!mediaPreviewArea) return;

                // 生成媒体预览HTML
                let mediaPreviewHTML = '';
                if (keywordItem.tempData.media && keywordItem.tempData.media.length > 0) {
                    mediaPreviewHTML = keywordItem.tempData.media.map(media => {
                        // 确保 URL 是有效的
                        const mediaUrl = media.url || '';
                        const fileSize = media.fileSize ? `${(media.fileSize / (1024 * 1024)).toFixed(2)} MB` : '';

                        if (media.type === 'image') {
                            // 图片预览 - 使用与视频类似的结构
                            return `
                                <div class="media-preview-item">
                                    <div class="media-preview-delete">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <div class="image-placeholder" data-media-type="image" data-media-url="${mediaUrl}">
                                        <div class="image-thumbnail">
                                            <img src="${mediaUrl}" alt="${media.name || '图片'}">
                                            <div class="image-overlay">
                                                <i class="fas fa-search-plus"></i>
                                            </div>
                                        </div>
                                        <div class="image-info">
                                            <div class="image-name">${media.name || '图片'}</div>
                                            ${fileSize ? `<div class="image-size">${fileSize}</div>` : ''}
                                        </div>
                                    </div>
                                    <div class="media-preview-name">${media.name || '图片'}</div>
                                </div>
                            `;
                        } else if (media.type === 'video') {
                            // 视频预览 - 使用占位符和下载按钮
                            return `
                                <div class="media-preview-item">
                                    <div class="media-preview-delete">
                                        <i class="fas fa-times"></i>
                                    </div>
                                    <div class="video-placeholder" data-media-type="video" data-media-url="${mediaUrl}">
                                        <div class="video-thumbnail">
                                            <video src="${mediaUrl}" preload="metadata"></video>
                                            <div class="video-overlay">
                                                <i class="fas fa-play-circle"></i>
                                            </div>
                                        </div>
                                        <div class="video-info">
                                            <div class="video-name">${media.name || '视频'}</div>
                                            ${fileSize ? `<div class="video-size">${fileSize}</div>` : ''}
                                        </div>
                                    </div>
                                    <div class="media-preview-name">${media.name || '视频'}</div>
                                </div>
                            `;
                        }
                    }).join('');
                } else {
                    mediaPreviewArea.innerHTML = `
                        <div class="media-preview-placeholder">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <div>暂无媒体文件，可点击下方按钮上传</div>
                        </div>
                    `;
                    mediaPreviewArea.classList.add('empty-preview');
                    return;
                }

                // 更新预览区域内容
                mediaPreviewArea.innerHTML = mediaPreviewHTML;
                mediaPreviewArea.classList.remove('empty-preview');

                // 添加媒体预览删除按钮事件
                const deleteButtons = mediaPreviewArea.querySelectorAll('.media-preview-delete');
                deleteButtons.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const mediaItem = this.closest('.media-preview-item');
                        const index = Array.from(mediaItem.parentNode.children).indexOf(mediaItem);

                        // 删除临时媒体项，但不修改实际数据
                        if (keywordItem.tempData.media && index >= 0 && index < keywordItem.tempData.media.length) {
                            keywordItem.tempData.media = keywordItem.tempData.media.filter((_, i) => i !== index);
                            mediaItem.remove();

                            // 如果没有媒体了，显示空状态
                            if (keywordItem.tempData.media.length === 0) {
                                updateMediaPreview(keywordItem);
                            }

                            // 标记已修改
                            keywordItem.dataset.tempState = 'modified';

                            // 高亮显示保存按钮，提示用户保存
                            const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                            if (saveBtn) {
                                saveBtn.classList.add('highlight');
                            }

                            showCustomAlert('媒体已临时删除，点击保存按钮后生效！');
                        }
                    });
                });

                // 添加图片和视频点击放大预览事件
                const mediaItems = mediaPreviewArea.querySelectorAll('.media-preview-item');
                mediaItems.forEach(item => {
                    // 为整个媒体项添加点击事件，而不仅仅是媒体元素
                    item.addEventListener('click', function(e) {
                        // 阻止删除按钮的点击事件冒泡
                        if (e.target.closest('.media-preview-delete')) {
                            return;
                        }

                        e.stopPropagation(); // 阻止事件冒泡

                        // 获取媒体元素（图片或视频占位符）
                        const mediaElement = item.querySelector('.image-placeholder, .video-placeholder');
                        if (!mediaElement) return;

                        const mediaType = mediaElement.dataset.mediaType;
                        const mediaUrl = mediaElement.dataset.mediaUrl;

                        if (!mediaType || !mediaUrl) {
                            console.error('无法获取媒体信息');
                            return;
                        }

                        // 获取模态框元素
                        const modal = document.getElementById('mediaPreviewModal');
                        const modalBody = document.getElementById('mediaPreviewModalBody');

                        // 清空模态框内容
                        modalBody.innerHTML = '';

                        // 根据媒体类型创建对应元素
                        if (mediaType === 'image') {
                            // 清空模态框内容
                            modalBody.innerHTML = '';

                            // 添加加载中提示
                            const loadingElement = document.createElement('div');
                            loadingElement.className = 'image-loading';
                            loadingElement.innerHTML = `
                                <div class="loading-spinner"></div>
                                <div class="loading-text">正在准备图片预览...</div>
                            `;
                            modalBody.appendChild(loadingElement);

                            // 添加下载按钮
                            const fileName = `image_${Date.now()}.jpg`;
                            const downloadContainer = document.createElement('div');
                            downloadContainer.className = 'image-download-option';
                            downloadContainer.innerHTML = `
                                <a href="${mediaUrl}" download="${fileName}" class="image-download-button">
                                    <i class="fas fa-download"></i> 下载图片
                                </a>
                            `;
                            modalBody.appendChild(downloadContainer);

                            // 使用主进程创建临时文件并返回文件URL
                            if (window.electronAPI && typeof window.electronAPI.saveBase64ToTemp === 'function') {
                                console.log('图片预览: 开始调用saveBase64ToTemp函数');
                                console.log('图片URL前缀:', mediaUrl.substring(0, 50));

                                // 调用主进程保存临时文件
                                // 确保Base64格式正确
                                if (!mediaUrl.startsWith('data:image/')) {
                                    console.log('图片URL格式不正确，尝试修正...');
                                    // 尝试修正格式
                                    if (mediaUrl.includes('base64,')) {
                                        const base64Content = mediaUrl.split('base64,')[1];
                                        mediaUrl = `data:image/jpeg;base64,${base64Content}`;
                                    } else if (mediaUrl.startsWith('http')) {
                                        // 如果是URL，不进行处理，直接返回
                                        console.log('检测到URL格式，不进行处理');
                                        // 在这种情况下，我们应该使用原始图片而不是创建临时文件
                                        // 返回一个特殊标记，表示这是一个URL而不是Base64
                                        return Promise.resolve(mediaUrl);
                                    } else {
                                        // 尝试将其作为Base64内容处理
                                        try {
                                            // 检查是否是有效的Base64字符串
                                            const testDecode = atob(mediaUrl);
                                            mediaUrl = `data:image/jpeg;base64,${mediaUrl}`;
                                        } catch (e) {
                                            console.error('无法将内容解析为Base64:', e);

                                            // 如果解析失败，尝试将其作为纯文本处理
                                            try {
                                                // 尝试将其作为图片数据处理
                                                mediaUrl = `data:image/jpeg;base64,${mediaUrl}`;
                                                console.log('尝试将其作为图片数据处理');
                                            } catch (textError) {
                                                console.error('尝试将其作为图片数据处理失败:', textError);
                                                // 如果所有尝试都失败，返回一个错误
                                                return Promise.reject(new Error('无效的图片数据格式'));
                                            }
                                        }
                                    }
                                    console.log('修正后的图片URL前缀:', mediaUrl.substring(0, 50));
                                }

                                window.electronAPI.saveBase64ToTemp(mediaUrl).then(fileUrl => {
                                    if (fileUrl) {
                                        console.log('创建临时文件成功:', fileUrl);

                                        // 移除加载中提示
                                        const loadingEl = modalBody.querySelector('.image-loading');
                                        if (loadingEl) loadingEl.remove();

                                        // 创建图片容器
                                        const imageContainer = document.createElement('div');
                                        imageContainer.className = 'image-preview-container';

                                        // 创建图片元素
                                        const imageElement = document.createElement('img');
                                        imageElement.className = 'image-preview-element';
                                        imageElement.src = fileUrl;
                                        imageElement.alt = '放大图片';

                                        // 将图片元素添加到容器中
                                        imageContainer.appendChild(imageElement);

                                        // 创建下载按钮容器
                                        const downloadButtonContainer = document.createElement('div');
                                        downloadButtonContainer.className = 'image-controls-container';

                                        // 复制下载按钮到新容器
                                        const downloadBtn = downloadContainer.querySelector('.image-download-button');
                                        if (downloadBtn) {
                                            const newDownloadBtn = downloadBtn.cloneNode(true);
                                            downloadButtonContainer.appendChild(newDownloadBtn);

                                            // 添加下载按钮点击事件
                                            newDownloadBtn.addEventListener('click', function() {
                                                console.log('开始下载图片:', fileName);
                                                // 延迟关闭模态框，给用户一些时间看到下载开始
                                                setTimeout(function() {
                                                    showCustomAlert('图片下载已开始，请查看您的下载文件夹');
                                                }, 500);
                                            });
                                        }

                                        // 将下载按钮容器添加到图片容器
                                        imageContainer.appendChild(downloadButtonContainer);

                                        // 添加图片容器到模态框
                                        modalBody.innerHTML = '';
                                        modalBody.appendChild(imageContainer);

                                        // 移除原来的下载容器
                                        if (downloadContainer.parentNode) {
                                            downloadContainer.parentNode.removeChild(downloadContainer);
                                        }

                                        // 添加清理函数，在模态框关闭时删除临时文件
                                        const closeBtn = modal.querySelector('.media-preview-modal-close');
                                        if (closeBtn) {
                                            const originalClickHandler = closeBtn.onclick;
                                            closeBtn.onclick = function() {
                                                // 删除临时文件
                                                window.electronAPI.deleteTempFile(fileUrl).then(success => {
                                                    if (success) {
                                                        console.log('已删除临时文件:', fileUrl);
                                                    }
                                                });

                                                // 调用原始处理程序
                                                if (typeof originalClickHandler === 'function') {
                                                    originalClickHandler();
                                                } else {
                                                    modal.classList.remove('show');
                                                }
                                            };
                                        }
                                    } else {
                                        // 移除加载中提示
                                        const loadingEl = modalBody.querySelector('.image-loading');
                                        if (loadingEl) loadingEl.remove();

                                        // 显示错误信息
                                        const errorElement = document.createElement('div');
                                        errorElement.className = 'image-error-message';
                                        errorElement.innerHTML = `
                                            <p>图片预览失败，请使用下方下载按钮下载后查看</p>
                                            <div class="error-details">错误信息: 无法创建临时文件</div>
                                        `;
                                        modalBody.insertBefore(errorElement, downloadContainer);
                                    }
                                }).catch(error => {
                                    console.error('创建临时文件错误:', error);

                                    // 移除加载中提示
                                    const loadingEl = modalBody.querySelector('.image-loading');
                                    if (loadingEl) loadingEl.remove();

                                    // 显示错误信息
                                    const errorElement = document.createElement('div');
                                    errorElement.className = 'image-error-message';
                                    errorElement.innerHTML = `
                                        <p>图片预览失败，请使用下方下载按钮下载后查看</p>
                                        <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                    `;
                                    modalBody.insertBefore(errorElement, downloadContainer);
                                });
                            } else {
                                // 移除加载中提示
                                const loadingEl = modalBody.querySelector('.image-loading');
                                if (loadingEl) loadingEl.remove();

                                // 创建一个优雅的图片信息和下载界面
                                const imageContainer = document.createElement('div');
                                imageContainer.className = 'image-preview-container';

                                // 创建图片元素
                                const imageElement = document.createElement('img');
                                imageElement.className = 'image-preview-element';
                                imageElement.src = mediaUrl;
                                imageElement.alt = '放大图片';

                                // 将图片元素添加到容器中
                                imageContainer.appendChild(imageElement);

                                // 创建下载按钮容器
                                const downloadButtonContainer = document.createElement('div');
                                downloadButtonContainer.className = 'image-controls-container';
                                downloadButtonContainer.innerHTML = `
                                    <a href="${mediaUrl}" download="${fileName}" class="image-download-button">
                                        <i class="fas fa-download"></i> 下载图片
                                    </a>
                                `;

                                // 将下载按钮容器添加到图片容器
                                imageContainer.appendChild(downloadButtonContainer);

                                // 添加图片容器到模态框
                                modalBody.innerHTML = '';
                                modalBody.appendChild(imageContainer);

                                // 添加下载按钮点击事件
                                const downloadBtn = downloadButtonContainer.querySelector('.image-download-button');
                                if (downloadBtn) {
                                    downloadBtn.addEventListener('click', function() {
                                        console.log('开始下载图片:', fileName);
                                        // 延迟关闭模态框，给用户一些时间看到下载开始
                                        setTimeout(function() {
                                            showCustomAlert('图片下载已开始，请查看您的下载文件夹');
                                        }, 500);
                                    });
                                }
                            }
                        } else if (mediaType === 'video') {
                            try {
                                // 清空模态框内容
                                modalBody.innerHTML = '';

                                // 添加加载中提示
                                const loadingElement = document.createElement('div');
                                loadingElement.className = 'video-loading';
                                loadingElement.innerHTML = `
                                    <div class="loading-spinner"></div>
                                    <div class="loading-text">正在准备视频预览...</div>
                                `;
                                modalBody.appendChild(loadingElement);

                                // 添加下载按钮
                                const fileName = `video_${Date.now()}.mp4`;
                                const downloadContainer = document.createElement('div');
                                downloadContainer.className = 'video-download-option';
                                downloadContainer.innerHTML = `
                                    <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                        <i class="fas fa-download"></i> 下载视频
                                    </a>
                                `;
                                modalBody.appendChild(downloadContainer);

                                // 使用主进程创建临时文件并返回文件URL
                                if (window.electronAPI && typeof window.electronAPI.saveBase64ToTemp === 'function') {
                                    // 调用主进程保存临时文件
                                    console.log('视频预览: 开始调用saveBase64ToTemp函数');
                                    console.log('视频URL前缀:', mediaUrl.substring(0, 50));

                                    // 确保Base64格式正确
                                    if (!mediaUrl.startsWith('data:video/')) {
                                        console.log('视频URL格式不正确，尝试修正...');
                                        // 尝试修正格式
                                        if (mediaUrl.includes('base64,')) {
                                            const base64Content = mediaUrl.split('base64,')[1];
                                            mediaUrl = `data:video/mp4;base64,${base64Content}`;
                                        } else if (mediaUrl.startsWith('http')) {
                                            // 如果是URL，不进行处理，直接返回
                                            console.log('检测到URL格式，不进行处理');
                                            // 在这种情况下，我们应该使用原始视频而不是创建临时文件
                                            // 返回一个特殊标记，表示这是一个URL而不是Base64
                                            return Promise.resolve(mediaUrl);
                                        } else {
                                            // 尝试将其作为Base64内容处理
                                            try {
                                                // 检查是否是有效的Base64字符串
                                                const testDecode = atob(mediaUrl);
                                                mediaUrl = `data:video/mp4;base64,${mediaUrl}`;
                                            } catch (e) {
                                                console.error('无法将内容解析为Base64:', e);

                                                // 如果解析失败，尝试将其作为纯文本处理
                                                try {
                                                    // 尝试将其作为视频数据处理
                                                    mediaUrl = `data:video/mp4;base64,${mediaUrl}`;
                                                    console.log('尝试将其作为视频数据处理');
                                                } catch (textError) {
                                                    console.error('尝试将其作为视频数据处理失败:', textError);
                                                    // 如果所有尝试都失败，返回一个错误
                                                    return Promise.reject(new Error('无效的视频数据格式'));
                                                }
                                            }
                                        }
                                        console.log('修正后的视频URL前缀:', mediaUrl.substring(0, 50));
                                    }

                                    window.electronAPI.saveBase64ToTemp(mediaUrl).then(fileUrl => {
                                        if (fileUrl) {
                                            console.log('创建临时文件成功:', fileUrl);

                                            // 移除加载中提示
                                            const loadingEl = modalBody.querySelector('.video-loading');
                                            if (loadingEl) loadingEl.remove();

                                            // 创建视频容器
                                            const videoContainer = document.createElement('div');
                                            videoContainer.className = 'video-preview-container';

                                            // 创建视频元素
                                            const videoElement = document.createElement('video');
                                            videoElement.className = 'video-preview-element';
                                            videoElement.controls = true;
                                            videoElement.autoplay = true;

                                            // 将视频元素添加到容器中
                                            videoContainer.appendChild(videoElement);

                                            // 添加事件监听器
                                            videoElement.addEventListener('loadeddata', function() {
                                                console.log('视频数据已加载');
                                            });

                                            videoElement.addEventListener('error', function(e) {
                                                const errorCode = e.target.error ? e.target.error.code : 'unknown';
                                                const errorMessage = e.target.error ? e.target.error.message : 'unknown error';
                                                console.error('视频加载错误:', e);
                                                console.error('错误代码:', errorCode);
                                                console.error('错误信息:', errorMessage);

                                                // 显示错误信息
                                                videoElement.style.display = 'none';
                                                const errorElement = document.createElement('div');
                                                errorElement.className = 'video-error-message';
                                                errorElement.innerHTML = `
                                                    <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                                    <div class="error-details">错误信息: ${errorMessage || '未知错误'}</div>
                                                    <div class="error-details">错误代码: ${errorCode}</div>
                                                `;
                                                modalBody.insertBefore(errorElement, downloadContainer);
                                            });

                                            // 设置视频源
                                            videoElement.src = fileUrl;

                                            // 创建下载按钮容器
                                            const downloadButtonContainer = document.createElement('div');
                                            downloadButtonContainer.className = 'video-controls-container';

                                            // 复制下载按钮到新容器
                                            const downloadBtn = downloadContainer.querySelector('.video-download-button');
                                            if (downloadBtn) {
                                                const newDownloadBtn = downloadBtn.cloneNode(true);
                                                downloadButtonContainer.appendChild(newDownloadBtn);

                                                // 添加下载按钮点击事件
                                                newDownloadBtn.addEventListener('click', function() {
                                                    console.log('开始下载视频');
                                                    // 延迟关闭模态框，给用户一些时间看到下载开始
                                                    setTimeout(function() {
                                                        showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                                    }, 500);
                                                });
                                            }

                                            // 将下载按钮容器添加到视频容器
                                            videoContainer.appendChild(downloadButtonContainer);

                                            // 添加视频容器到模态框
                                            modalBody.innerHTML = '';
                                            modalBody.appendChild(videoContainer);

                                            // 移除原来的下载容器
                                            if (downloadContainer.parentNode) {
                                                downloadContainer.parentNode.removeChild(downloadContainer);
                                            }

                                            // 添加清理函数，在模态框关闭时删除临时文件
                                            const closeBtn = modal.querySelector('.media-preview-modal-close');
                                            if (closeBtn) {
                                                const originalClickHandler = closeBtn.onclick;
                                                closeBtn.onclick = function() {
                                                    // 删除临时文件
                                                    window.electronAPI.deleteTempFile(fileUrl).then(success => {
                                                        if (success) {
                                                            console.log('已删除临时文件:', fileUrl);
                                                        }
                                                    });

                                                    // 调用原始处理程序
                                                    if (typeof originalClickHandler === 'function') {
                                                        originalClickHandler();
                                                    } else {
                                                        modal.classList.remove('show');
                                                    }
                                                };
                                            }
                                        } else {
                                            // 移除加载中提示
                                            const loadingEl = modalBody.querySelector('.video-loading');
                                            if (loadingEl) loadingEl.remove();

                                            // 显示错误信息
                                            const errorElement = document.createElement('div');
                                            errorElement.className = 'video-error-message';
                                            errorElement.innerHTML = `
                                                <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                                <div class="error-details">错误信息: 无法创建临时文件</div>
                                            `;
                                            modalBody.insertBefore(errorElement, downloadContainer);
                                        }
                                    }).catch(error => {
                                        console.error('创建临时文件错误:', error);

                                        // 移除加载中提示
                                        const loadingEl = modalBody.querySelector('.video-loading');
                                        if (loadingEl) loadingEl.remove();

                                        // 显示错误信息
                                        const errorElement = document.createElement('div');
                                        errorElement.className = 'video-error-message';
                                        errorElement.innerHTML = `
                                            <p>视频预览失败，请使用下方下载按钮下载后查看</p>
                                            <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                        `;
                                        modalBody.insertBefore(errorElement, downloadContainer);
                                    });
                                } else {
                                    // 移除加载中提示
                                    const loadingEl = modalBody.querySelector('.video-loading');
                                    if (loadingEl) loadingEl.remove();

                                    // 创建一个优雅的视频信息和下载界面
                                    const fileName = `video_${Date.now()}.mp4`;

                                    // 创建视频容器
                                    const videoContainer = document.createElement('div');
                                    videoContainer.className = 'video-preview-container';

                                    // 添加视频信息
                                    const videoInfo = document.createElement('div');
                                    videoInfo.className = 'video-info-container';
                                    videoInfo.innerHTML = `
                                        <div class="video-icon-large">
                                            <i class="fas fa-file-video"></i>
                                        </div>
                                        <div class="video-info-text">
                                            <h3>视频文件</h3>
                                            <p>由于Electron API不可用，无法直接预览视频。</p>
                                            <p>请使用下方按钮下载视频后在本地播放器中查看。</p>
                                        </div>
                                    `;
                                    videoContainer.appendChild(videoInfo);

                                    // 创建下载按钮容器
                                    const downloadButtonContainer = document.createElement('div');
                                    downloadButtonContainer.className = 'video-controls-container';
                                    downloadButtonContainer.innerHTML = `
                                        <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                            <i class="fas fa-download"></i> 下载视频
                                        </a>
                                    `;
                                    videoContainer.appendChild(downloadButtonContainer);

                                    // 添加视频容器到模态框
                                    modalBody.innerHTML = '';
                                    modalBody.appendChild(videoContainer);

                                    // 添加下载按钮点击事件
                                    const downloadBtn = downloadButtonContainer.querySelector('.video-download-button');
                                    if (downloadBtn) {
                                        downloadBtn.addEventListener('click', function() {
                                            console.log('开始下载视频:', fileName);
                                            // 延迟关闭模态框，给用户一些时间看到下载开始
                                            setTimeout(function() {
                                                showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                            }, 500);
                                        });
                                    }

                                    // 移除原来的下载容器
                                    if (downloadContainer.parentNode) {
                                        downloadContainer.parentNode.removeChild(downloadContainer);
                                    }
                                }

                                // 添加下载按钮点击事件
                                const downloadBtn = modalBody.querySelector('.video-download-button');
                                if (downloadBtn) {
                                    downloadBtn.addEventListener('click', function() {
                                        console.log('开始下载视频:', fileName);
                                        // 延迟关闭模态框，给用户一些时间看到下载开始
                                        setTimeout(function() {
                                            showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                        }, 500);
                                    });
                                }
                            } catch (error) {
                                console.error('创建视频预览失败:', error);

                                // 如果出错，显示下载选项
                                const fileName = `video_${Date.now()}.mp4`;

                                // 创建视频容器
                                const videoContainer = document.createElement('div');
                                videoContainer.className = 'video-preview-container';

                                // 添加错误信息
                                const errorInfo = document.createElement('div');
                                errorInfo.className = 'video-error-message';
                                errorInfo.innerHTML = `
                                    <p>视频预览失败，请使用下方按钮下载后查看</p>
                                    <div class="error-details">错误信息: ${error.message || '未知错误'}</div>
                                `;
                                videoContainer.appendChild(errorInfo);

                                // 创建下载按钮容器
                                const downloadButtonContainer = document.createElement('div');
                                downloadButtonContainer.className = 'video-controls-container';
                                downloadButtonContainer.innerHTML = `
                                    <a href="${mediaUrl}" download="${fileName}" class="video-download-button">
                                        <i class="fas fa-download"></i> 下载视频
                                    </a>
                                `;
                                videoContainer.appendChild(downloadButtonContainer);

                                // 添加视频容器到模态框
                                modalBody.innerHTML = '';
                                modalBody.appendChild(videoContainer);

                                // 添加下载按钮点击事件
                                const downloadBtn = downloadButtonContainer.querySelector('.video-download-button');
                                if (downloadBtn) {
                                    downloadBtn.addEventListener('click', function() {
                                        console.log('开始下载视频:', fileName);
                                        // 延迟关闭模态框，给用户一些时间看到下载开始
                                        setTimeout(function() {
                                            showCustomAlert('视频下载已开始，请查看您的下载文件夹');
                                        }, 500);
                                    });
                                }
                            }
                        }

                        // 显示模态框
                        modal.classList.add('show');

                        // 添加关闭按钮事件
                        const closeBtn = modal.querySelector('.media-preview-modal-close');
                        closeBtn.addEventListener('click', function() {
                            modal.classList.remove('show');
                        });

                        // 点击模态框背景关闭
                        modal.addEventListener('click', function(e) {
                            if (e.target === modal) {
                                modal.classList.remove('show');
                            }
                        });
                    });
                });
            }

            // 添加新的关键词
            function addNewKeyword() {
                if (window.activeCategory === null) return; // 未选中分类时不添加

                // 创建新的关键词项，但不立即保存到数据中
                const newKeywordId = 'kw' + new Date().getTime();

                // 更新UI，添加新的临时关键词项
                const keywordReplyContent = document.querySelector('.keyword-reply-content');

                // 检查是否显示空状态消息，如果是则清空内容区域
                const emptyStateMessages = keywordReplyContent.querySelectorAll('.empty-state-message');
                if (emptyStateMessages.length > 0) {
                    console.log('检测到空状态消息，正在清除...');
                    keywordReplyContent.innerHTML = '';
                }

                const keywordItem = document.createElement('div');
                keywordItem.className = 'keyword-reply-item';
                keywordItem.dataset.id = newKeywordId;
                keywordItem.dataset.tempState = 'new'; // 标记为新创建状态

                // 初始化临时数据
                keywordItem.tempData = {
                    keyword: '',
                    reply: '',
                    media: []
                };

                // 生成HTML
                keywordItem.innerHTML = `
                    <div class="keyword-reply-form">
                        <div class="keyword-form-group">
                            <div class="keyword-form-label">关键词:</div>
                            <input type="text" class="keyword-form-input" value="" placeholder="关键词编辑框">
                        </div>
                        <div class="keyword-form-group">
                            <div class="keyword-form-label">回复:</div>
                            <input type="text" class="keyword-form-input" value="" placeholder="回复内容编辑框">
                        </div>

                        <div class="media-preview-area empty-preview">
                            <div class="media-preview-placeholder">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <div>暂无媒体文件，可点击下方按钮上传</div>
                            </div>
                        </div>

                        <div class="media-upload-buttons">
                            <button class="media-upload-btn">
                                <i class="fas fa-image"></i>上传图片
                            </button>
                            <button class="media-upload-btn">
                                <i class="fas fa-video"></i>上传视频
                            </button>
                            <button class="keyword-save-btn highlight" data-id="${newKeywordId}">
                                <i class="fas fa-save"></i>保存
                            </button>
                            <button class="media-delete-btn" data-id="${newKeywordId}">
                                <i class="fas fa-trash-alt"></i>删除
                            </button>
                        </div>
                    </div>
                `;

                // 添加删除按钮事件
                keywordItem.querySelector('.media-delete-btn').addEventListener('click', function() {
                    // 如果是未保存的新项，直接从UI中移除
                    if (keywordItem.dataset.tempState === 'new') {
                        keywordItem.remove();
                        showCustomAlert('已取消新建关键词');
                        // 如果没有其他关键词项，显示空状态
                        if (keywordReplyContent.children.length === 0) {
                            const emptyState = document.createElement('div');
                            emptyState.className = 'keyword-reply-item';
                            emptyState.innerHTML = `
                                <div class="empty-state-message">
                                    <i class="fas fa-comment-slash"></i>
                                    <div class="title">暂无关键词回复</div>
                                    <div class="subtitle">可点击右上角添加按钮，添加关键词回复</div>
                                </div>
                            `;
                            keywordReplyContent.appendChild(emptyState);
                        }
                    } else {
                        deleteKeyword(this.dataset.id);
                    }
                });

                // 添加保存按钮事件
                keywordItem.querySelector('.keyword-save-btn').addEventListener('click', function() {
                    saveKeyword(this.dataset.id, keywordItem);
                });

                // 添加输入框值改变事件
                const inputs = keywordItem.querySelectorAll('.keyword-form-input');
                inputs.forEach((input, i) => {
                    input.addEventListener('input', function() {
                        // 更新临时数据但不提交到实际数据
                        if (i === 0) keywordItem.tempData.keyword = this.value;
                        if (i === 1) keywordItem.tempData.reply = this.value;

                        // 标记编辑框已被修改
                        keywordItem.dataset.tempState = 'modified';

                        // 高亮显示保存按钮，提示用户保存
                        const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                        saveBtn.classList.add('highlight');
                    });
                });

                // 添加上传按钮事件
                const uploadButtons = keywordItem.querySelectorAll('.media-upload-btn');
                uploadButtons.forEach((btn, index) => {
                    btn.addEventListener('click', function() {
                        // 模拟上传文件
                        simulateMediaUpload(keywordItem, index === 0 ? 'image' : 'video');
                    });
                });

                // 添加到DOM的开头位置
                if (keywordReplyContent.firstChild) {
                    keywordReplyContent.insertBefore(keywordItem, keywordReplyContent.firstChild);
                } else {
                    keywordReplyContent.appendChild(keywordItem);
                }

                // 聚焦到第一个输入框
                const firstInput = keywordItem.querySelector('.keyword-form-input');
                if (firstInput) {
                    firstInput.focus();
                }

                // 更新右侧关键词列表
                updateKeywordCountAndList();

                showCustomAlert('已创建新关键词，编辑后点击保存按钮生效');
            }

            // 删除关键词
            function deleteKeyword(keywordId) {
                // 如果传入的是DOM元素，则获取其ID
                if (typeof keywordId === 'object' && keywordId.dataset && keywordId.dataset.id) {
                    keywordId = keywordId.dataset.id;
                }

                // 确认删除
                showCustomConfirm('确定要删除此关键词吗？删除后无法恢复。', function(confirmed) {
                    if (!confirmed) return;

                    // 删除关键词
                    const categoryKeywords = window.keywordData[window.activeCategory] || [];
                    const index = categoryKeywords.findIndex(kw => kw.id === keywordId);

                    if (index !== -1) {
                        // 创建一个新数组，而不是修改原数组
                        window.keywordData[window.activeCategory] = categoryKeywords.filter(kw => kw.id !== keywordId);

                        // 从本地存储中删除
                        if (typeof categoryStorageAdapter !== 'undefined') {
                            console.log(`从本地存储中删除关键词: 分类ID=${window.activeCategory}, 关键词ID=${keywordId}`);

                            // 使用新的deleteKeywordReply方法
                            categoryStorageAdapter.deleteKeywordReply(window.activeCategory, keywordId);
                        }

                        // 更新UI
                        renderKeywordList();
                        updateKeywordCountAndList();

                        showCustomAlert('删除关键词成功！');
                    }
                });
            }

            // 更新关键词总数和列表
            function updateKeywordCountAndList() {
                console.log('调用全局updateKeywordCountAndList函数');

                const countElement = document.querySelector('.keyword-count-value');
                const keywordListBox = document.querySelector('.keyword-list-box');
                const titleElement = document.querySelector('.keyword-list-title');

                if (!countElement || !keywordListBox || !titleElement) {
                    console.warn('找不到关键词列表相关DOM元素');
                    return;
                }

                // 计算所有分类组的关键词总数
                let totalKeywords = 0;
                if (typeof keywordData === 'object' && keywordData !== null) {
                    Object.values(keywordData).forEach(categoryKeywords => {
                        if (Array.isArray(categoryKeywords)) {
                            totalKeywords += categoryKeywords.length;
                        }
                    });
                }
                // 更新总数显示
                countElement.textContent = `${totalKeywords}个`;
            }

            // 添加分类按钮事件
            document.getElementById('addCategoryBtn').addEventListener('click', function() {
                addNewCategory();
            });

            // 添加关键词按钮事件
            document.getElementById('addKeywordBtn').addEventListener('click', addNewKeyword);

            // 特殊区复选框事件
            const specialTriggerCheck = document.getElementById('specialTriggerCheck');
            if (specialTriggerCheck) {
                // 加载特殊设置
                if (window.specialSettings && typeof window.specialSettings.sendTriggerWords !== 'undefined') {
                    specialTriggerCheck.checked = window.specialSettings.sendTriggerWords;
                    console.log('已加载特殊触发词设置:', window.specialSettings.sendTriggerWords);
                }

                specialTriggerCheck.addEventListener('change', function() {
                    // 保存设置到全局变量
                    if (!window.specialSettings) {
                        window.specialSettings = {};
                    }
                    window.specialSettings.sendTriggerWords = this.checked;
                    console.log('特殊触发词设置已更改:', this.checked);

                    // 保存到本地存储
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        categoryStorageAdapter.saveSpecialSettings(window.specialSettings);
                        console.log('特殊触发词设置已保存到本地存储');
                    }
                });
            }

            // 初始化UI - 修改初始状态为未选中
            initCategoryList();
            setActiveCategory(null); // 初始设置为未选中状态
            renderKeywordList();
            updateKeywordCountAndList();

            // 设置初始化标志
            window.keywordPageInitialized = true;
            console.log('关键词页面初始化完成');
        }

        // 保存关键词
        function saveKeyword(keywordId, keywordItem) {
            console.log(`开始保存关键词: ${keywordId}`);

            // 直接使用传入的keywordItem或者查找它
            if (!keywordItem) {
                keywordItem = document.querySelector(`.keyword-reply-item[data-id="${keywordId}"]`);
            }

            if (!keywordItem) {
                console.error(`未找到ID为 ${keywordId} 的关键词元素`);
                showCustomAlert('保存失败：未找到关键词元素');
                return;
            }

            // 获取activeCategory
            if (window.activeCategory === null || window.activeCategory === undefined) {
                console.error('保存失败：未选中分类');
                showCustomAlert('保存失败：未选中分类');
                return;
            }

            // 只有在有修改时才保存
            if (keywordItem.dataset.tempState !== 'modified' && keywordItem.dataset.tempState !== 'new') {
                showCustomAlert('无需保存：未进行任何修改');
                return;
            }

            const categoryKeywords = window.keywordData[window.activeCategory] || [];
            console.log(`当前分类 ${window.activeCategory} 包含 ${categoryKeywords.length} 个关键词`);

            const keywordIndex = categoryKeywords.findIndex(kw => kw.id === keywordId);
            console.log(`找到关键词索引: ${keywordIndex}`);

            let keywordObj;

            if (keywordIndex !== -1) {
                // 从临时数据创建更新后的关键词对象
                keywordObj = {
                    id: keywordId,
                    keyword: keywordItem.tempData.keyword,
                    reply: keywordItem.tempData.reply,
                    media: JSON.parse(JSON.stringify(keywordItem.tempData.media || []))
                };

                // 确保媒体数据中的Base64字符串被正确保存
                if (keywordObj.media && keywordObj.media.length > 0) {
                    console.log(`关键词 ${keywordId} 包含 ${keywordObj.media.length} 个媒体文件`);

                    // 确保每个媒体文件的URL格式正确
                    keywordObj.media.forEach((media, index) => {
                        if (media.url) {
                            // 强制设置 isBase64 属性，确保它存在
                            if (media.type === 'image' || media.type === 'video') {
                                if (media.url.includes('base64') || !media.url.startsWith('http')) {
                                    media.isBase64 = true;
                                }
                            }

                            // 如果是Base64格式，确保有正确的MIME类型前缀
                            if (media.type === 'image' && !media.url.startsWith('data:image/')) {
                                if (media.url.includes('base64,')) {
                                    const base64Content = media.url.split('base64,')[1];
                                    media.url = `data:image/jpeg;base64,${base64Content}`;
                                    media.isBase64 = true;
                                } else if (!media.url.startsWith('http')) {
                                    try {
                                        // 检查是否是有效的Base64字符串
                                        const testDecode = atob(media.url);
                                        media.url = `data:image/jpeg;base64,${media.url}`;
                                        media.isBase64 = true;
                                    } catch (e) {
                                        console.error(`媒体文件 ${index} 的URL格式无效:`, e);
                                    }
                                }
                                console.log(`已修正图片媒体 ${index} 的URL格式`);
                            } else if (media.type === 'video' && !media.url.startsWith('data:video/')) {
                                if (media.url.includes('base64,')) {
                                    const base64Content = media.url.split('base64,')[1];
                                    media.url = `data:video/mp4;base64,${base64Content}`;
                                    media.isBase64 = true;
                                } else if (!media.url.startsWith('http')) {
                                    try {
                                        // 检查是否是有效的Base64字符串
                                        const testDecode = atob(media.url);
                                        media.url = `data:video/mp4;base64,${media.url}`;
                                        media.isBase64 = true;
                                    } catch (e) {
                                        console.error(`媒体文件 ${index} 的URL格式无效:`, e);
                                    }
                                }
                                console.log(`已修正视频媒体 ${index} 的URL格式`);
                            }

                            // 打印调试信息
                            console.log(`媒体 ${index} 类型: ${media.type}, isBase64: ${media.isBase64}, URL前缀: ${media.url.substring(0, 50)}`);
                        }
                    });
                }

                // 替换原有数据
                categoryKeywords[keywordIndex] = keywordObj;

                console.log(`保存的关键词值: ${keywordObj.keyword}, 回复: ${keywordObj.reply}, 媒体数: ${keywordObj.media.length}`);

                // 清除修改标记
                keywordItem.dataset.tempState = 'saved';

                // 移除保存按钮高亮
                const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                if (saveBtn) {
                    saveBtn.classList.remove('highlight');
                }

                // 仅更新右侧关键词列表，不重新渲染整个左侧
                if (typeof updateKeywordCountAndList === 'function') {
                    updateKeywordCountAndList();
                }

                // 保存到本地存储
                if (typeof categoryStorageAdapter !== 'undefined') {
                    categoryStorageAdapter.saveKeywordReply(window.activeCategory, keywordId, keywordObj);
                    console.log(`关键词已保存到本地存储: 分类ID=${window.activeCategory}, 关键词ID=${keywordId}`);
                }

                // 显示保存成功提示
                showCustomAlert('关键词保存成功！');
            } else {
                // 如果是新添加的关键词（尚未在数据中）
                keywordObj = {
                    id: keywordId,
                    keyword: keywordItem.tempData.keyword,
                    reply: keywordItem.tempData.reply,
                    media: JSON.parse(JSON.stringify(keywordItem.tempData.media || []))
                };

                // 确保媒体数据中的Base64字符串被正确保存
                if (keywordObj.media && keywordObj.media.length > 0) {
                    console.log(`新关键词 ${keywordId} 包含 ${keywordObj.media.length} 个媒体文件`);

                    // 确保每个媒体文件的URL格式正确
                    keywordObj.media.forEach((media, index) => {
                        if (media.url) {
                            // 强制设置 isBase64 属性，确保它存在
                            if (media.type === 'image' || media.type === 'video') {
                                if (media.url.includes('base64') || !media.url.startsWith('http')) {
                                    media.isBase64 = true;
                                }
                            }

                            // 如果是Base64格式，确保有正确的MIME类型前缀
                            if (media.type === 'image' && !media.url.startsWith('data:image/')) {
                                if (media.url.includes('base64,')) {
                                    const base64Content = media.url.split('base64,')[1];
                                    media.url = `data:image/jpeg;base64,${base64Content}`;
                                    media.isBase64 = true;
                                } else if (!media.url.startsWith('http')) {
                                    try {
                                        // 检查是否是有效的Base64字符串
                                        const testDecode = atob(media.url);
                                        media.url = `data:image/jpeg;base64,${media.url}`;
                                        media.isBase64 = true;
                                    } catch (e) {
                                        console.error(`媒体文件 ${index} 的URL格式无效:`, e);
                                    }
                                }
                                console.log(`已修正图片媒体 ${index} 的URL格式`);
                            } else if (media.type === 'video' && !media.url.startsWith('data:video/')) {
                                if (media.url.includes('base64,')) {
                                    const base64Content = media.url.split('base64,')[1];
                                    media.url = `data:video/mp4;base64,${base64Content}`;
                                    media.isBase64 = true;
                                } else if (!media.url.startsWith('http')) {
                                    try {
                                        // 检查是否是有效的Base64字符串
                                        const testDecode = atob(media.url);
                                        media.url = `data:video/mp4;base64,${media.url}`;
                                        media.isBase64 = true;
                                    } catch (e) {
                                        console.error(`媒体文件 ${index} 的URL格式无效:`, e);
                                    }
                                }
                                console.log(`已修正视频媒体 ${index} 的URL格式`);
                            }

                            // 打印调试信息
                            console.log(`媒体 ${index} 类型: ${media.type}, isBase64: ${media.isBase64}, URL前缀: ${media.url.substring(0, 50)}`);
                        }
                    });
                }

                // 将新关键词添加到数据中
                categoryKeywords.push(keywordObj);
                window.keywordData[window.activeCategory] = categoryKeywords;

                // 清除修改标记
                keywordItem.dataset.tempState = 'saved';

                // 移除保存按钮高亮
                const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                if (saveBtn) {
                    saveBtn.classList.remove('highlight');
                }

                // 更新UI
                if (typeof updateKeywordCountAndList === 'function') {
                    updateKeywordCountAndList();
                }

                // 保存到本地存储
                if (typeof categoryStorageAdapter !== 'undefined') {
                    categoryStorageAdapter.saveKeywordReply(window.activeCategory, keywordId, keywordObj);
                    console.log(`新增关键词已保存到本地存储: 分类ID=${window.activeCategory}, 关键词ID=${keywordId}`);
                }

                // 显示保存成功提示
                showCustomAlert('新增关键词保存成功！');
            }
        }

        // 添加特殊区的HTML
        function renderSpecialArea() {
            const specialArea = document.querySelector('.special-area');

            if (!specialArea) return;

            // 设置特殊区域整体样式
            specialArea.style.padding = '12px';
            specialArea.style.overflow = 'hidden';
            specialArea.style.display = 'flex';
            specialArea.style.flexDirection = 'column';
            specialArea.style.gap = '12px'; // 统一盒子之间的间距为12px

            // 初始化HTML结构，内嵌样式
            specialArea.innerHTML = `
                <div class="special-box" style="padding: 12px; margin: 0; background-color: #f9f9f9; border: 1px solid #eee; border-radius: 6px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);">
                    <div class="checkbox-wrapper">
                        <label class="custom-checkbox-label">
                            <input type="checkbox" id="specialTriggerCheck">
                            <span class="custom-checkbox-text">去该分类组发言时添加特殊触发按钮</span>
                        </label>
                    </div>
                </div>

                <div class="keyword-count-box" style="padding: 12px; margin: 0; background-color: #f9f9f9; border: 1px solid #eee; border-radius: 6px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03); display: flex; align-items: center; justify-content: space-between;">
                    <div class="keyword-count-title" style="font-weight: 600; margin: 0; margin-right: 10px;">关键词数量</div>
                    <div class="keyword-count-value" style="color: #4a6bdf; font-weight: 600; font-size: 18px;">0个</div>
                </div>

                <div class="keyword-list-box" style="flex: 1; display: flex; flex-direction: column; margin: 0; background-color: #f9f9f9; border: 1px solid #eee; border-radius: 6px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03); overflow: hidden; min-height: 0;">
                    <div class="keyword-list-title" style="padding: 12px 12px 12px 0; font-weight: 600; border-bottom: 1px solid #eee; display: flex; align-items: center; flex-shrink: 0;">
                        <i class="fas fa-list" style="margin-right: 8px;"></i>关键词列表<span class="category-name" style="font-size: 13px; color: #666; margin-left: 5px; font-weight: normal;">（未选中分类组）</span>
                    </div>
                    <div class="keyword-list-container" style="flex: 1; overflow-y: auto; padding: 12px; min-height: 0;">
                        <div class="no-selection-message" style="padding: 10px; color: #666;">请先选择一个关键词分类组</div>
                    </div>
                </div>
            `;

            // 绑定特殊触发词复选框事件
            const specialTriggerCheck = document.getElementById('specialTriggerCheck');
            if (specialTriggerCheck) {
                // 加载特殊设置
                if (window.specialSettings && typeof window.specialSettings.sendTriggerWords !== 'undefined') {
                    specialTriggerCheck.checked = window.specialSettings.sendTriggerWords;
                    console.log('已加载特殊触发词设置:', window.specialSettings.sendTriggerWords);
                }

                specialTriggerCheck.addEventListener('change', function() {
                    // 保存设置到全局变量
                    if (!window.specialSettings) {
                        window.specialSettings = {};
                    }
                    window.specialSettings.sendTriggerWords = this.checked;
                    console.log('特殊触发词设置已更改:', this.checked);

                    // 保存到本地存储
                    if (typeof categoryStorageAdapter !== 'undefined') {
                        categoryStorageAdapter.saveSpecialSettings(window.specialSettings);
                        console.log('特殊触发词设置已保存到本地存储');
                    }
                });
            }

            // 确保关键词列表占据更多空间
            const listBox = document.querySelector('.keyword-list-box');
            if (listBox) {
                // 计算特殊区域的可用高度
                const specialAreaHeight = specialArea.clientHeight;
                // 减去其他元素的高度和间距
                const otherElementsHeight =
                    document.querySelector('.special-box').offsetHeight +
                    document.querySelector('.keyword-count-box').offsetHeight +
                    24; // 两个12px的间距

                // 设置关键词列表盒子的高度
                const availableHeight = specialAreaHeight - otherElementsHeight;
                if (availableHeight > 200) { // 确保至少有合理的最小高度
                    listBox.style.height = `${availableHeight}px`;
                } else {
                    listBox.style.flex = '1';
                }
            }

            // 初始化关键词数量和列表
            updateKeywordCountAndList();

            // 不再需要额外调用applySpecialAreaStyles函数，因为所有样式已内嵌到HTML中
        }

        // 模拟媒体上传
        function simulateMediaUpload(keywordItem, keywordInput, replyInput) {
            console.log('模拟上传媒体');

            // 创建一个随机的媒体对象
            const randomType = Math.random() > 0.5 ? 'image' : 'video';
            const randomId = Date.now().toString();
            const newMedia = {
                id: randomId,
                type: randomType,
                url: `https://example.com/${randomType}/${randomId}.${randomType === 'image' ? 'jpg' : 'mp4'}`,
                thumbnail: `https://example.com/thumbnail/${randomId}.jpg`
            };

            // 将新媒体添加到临时数据中
            keywordItem.tempData.media.push(newMedia);

            // 标记为已修改
            keywordItem.dataset.tempState = 'modified';
            keywordItem.querySelector('.keyword-save-btn').classList.add('highlight');

            // 更新媒体预览区域，不重新渲染整个列表
            const mediaPreview = keywordItem.querySelector('.media-preview');
            updateMediaPreview(mediaPreview, keywordItem.tempData.media);

            // 恢复输入框的值
            const inputs = keywordItem.querySelectorAll('.keyword-input');
            inputs[0].value = keywordInput;
            inputs[1].value = replyInput;

            // 实时更新右侧关键词列表
            updateKeywordCountAndList();
        }

        // 添加新关键词
        function addNewKeyword() {
                if (window.activeCategory === null) {
                alert('请先选择一个分类');
                return;
            }

            // 创建一个临时ID
            const tempId = `temp_${Date.now()}`;

            // 创建UI元素
            const keywordItem = createKeywordItem(tempId);
            keywordItem.dataset.tempState = 'new'; // 标记为新建项

            // 将新项添加到列表开头
            const keywordList = document.querySelector('.keyword-reply-list');
            keywordList.insertBefore(keywordItem, keywordList.firstChild);

            // 聚焦到新建的关键词输入框
            const input = keywordItem.querySelector('.keyword-input');
            input.focus();

            // 滚动到顶部
            keywordList.scrollTop = 0;

            // 更新右侧列表
            updateKeywordCountAndList();
        }

        function createKeywordItem(id, keyword = '', reply = '', media = []) {
            const keywordItem = document.createElement('div');
            keywordItem.className = 'keyword-reply-item';
            keywordItem.dataset.id = id;

            // 创建临时数据对象
            keywordItem.tempData = {
                keyword: keyword,
                reply: reply,
                media: [...media]
            };

            // 初始状态为未修改
            keywordItem.dataset.tempState = 'initialized';

            // 创建关键词和回复输入框
            const inputGroup = document.createElement('div');
            inputGroup.className = 'keyword-input-group';

            // 创建输入框
            const inputs = ['关键词', '回复内容'].map((label, i) => {
                const wrapper = document.createElement('div');
                wrapper.className = 'keyword-input-wrapper';

                const labelElement = document.createElement('div');
                labelElement.className = 'keyword-input-label';
                labelElement.textContent = label;

                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'keyword-input';
                input.placeholder = `请输入${label}`;
                input.value = i === 0 ? keyword : reply;

                wrapper.appendChild(labelElement);
                wrapper.appendChild(input);

                return wrapper;
            });

            inputs.forEach(input => inputGroup.appendChild(input));
            keywordItem.appendChild(inputGroup);

            // 创建媒体预览区域
            const mediaPreview = document.createElement('div');
            mediaPreview.className = 'media-preview';
            updateMediaPreview(mediaPreview, media);
            keywordItem.appendChild(mediaPreview);

            // 创建按钮组
            const btnGroup = document.createElement('div');
            btnGroup.className = 'keyword-btn-group';

            // 上传媒体按钮
            const uploadBtn = document.createElement('button');
            uploadBtn.className = 'keyword-upload-btn';
            uploadBtn.textContent = '上传媒体';
            uploadBtn.addEventListener('click', () => {
                // 保存当前输入值
                const inputs = keywordItem.querySelectorAll('.keyword-input');
                const keywordInput = inputs[0].value;
                const replyInput = inputs[1].value;

                // 模拟上传媒体
                simulateMediaUpload(keywordItem, keywordInput, replyInput);
            });

            // 保存按钮
            const saveBtn = document.createElement('button');
            saveBtn.className = 'keyword-save-btn';
            saveBtn.textContent = '保存';
            saveBtn.addEventListener('click', () => {
                if (keywordItem.dataset && keywordItem.dataset.id) {
                    saveKeyword(keywordItem.dataset.id, keywordItem);
                } else {
                    console.error('保存失败：关键词元素缺少ID');
                    showCustomAlert('保存失败：关键词元素缺少ID');
                }
            });

            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'keyword-delete-btn';
            deleteBtn.textContent = '删除';
            deleteBtn.addEventListener('click', () => deleteKeyword(keywordItem));

            btnGroup.appendChild(uploadBtn);
            btnGroup.appendChild(saveBtn);
            btnGroup.appendChild(deleteBtn);
            keywordItem.appendChild(btnGroup);

            // 添加输入框值改变事件
            const inputElements = keywordItem.querySelectorAll('.keyword-input');
            inputElements.forEach((input, i) => {
                input.addEventListener('input', function() {
                    // 更新临时数据但不提交到实际数据
                    if (i === 0) keywordItem.tempData.keyword = this.value;
                    if (i === 1) keywordItem.tempData.reply = this.value;

                    // 标记编辑框已被修改
                    keywordItem.dataset.tempState = 'modified';

                    // 高亮显示保存按钮，提示用户保存
                    const saveBtn = keywordItem.querySelector('.keyword-save-btn');
                    saveBtn.classList.add('highlight');

                    // 实时更新右侧关键词列表
                    updateKeywordCountAndList();
                });
            });

            return keywordItem;
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 修改标题文本
            const countTitle = document.querySelector('.keyword-count-title');
            if (countTitle && countTitle.textContent === '关键词回复列表中的关键词总数') {
                countTitle.textContent = '关键词总数';
            }

            const listTitle = document.querySelector('.keyword-list-title');
            if (listTitle && listTitle.innerHTML.includes('关键词回复列表中的关键词')) {
                listTitle.innerHTML = '<i class="fas fa-list"></i>关键词列表' +
                    (listTitle.querySelector('.category-name') ?
                    listTitle.querySelector('.category-name').outerHTML : '');
            }
        });

        // 添加新函数:
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 修改标题文本
            const countTitle = document.querySelector('.keyword-count-title');
            if (countTitle && countTitle.textContent === '关键词回复列表中的关键词总数') {
                countTitle.textContent = '关键词总数';
            }

            const listTitle = document.querySelector('.keyword-list-title');
            if (listTitle && listTitle.innerHTML.includes('关键词回复列表中的关键词')) {
                listTitle.innerHTML = '<i class="fas fa-list"></i>关键词列表' +
                    (listTitle.querySelector('.category-name') ?
                    listTitle.querySelector('.category-name').outerHTML : '');
            }

            // 检查updateKeywordCountAndList函数是否存在
            if (typeof updateKeywordCountAndList !== 'function') {
                console.warn('updateKeywordCountAndList函数未定义，某些功能可能不可用');
            }

            // 监听关键词编辑区域的变化
            setUpKeywordChangeObserver();
        });

        // 设置关键词变更观察器
        function setUpKeywordChangeObserver() {
            // 使用MutationObserver监听关键词编辑区域的DOM变化
            const observer = new MutationObserver(function(mutations) {
                // 检查是否有关键词项被添加或修改
                let shouldUpdate = false;

                mutations.forEach(function(mutation) {
                    // 当节点被添加时
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (let i = 0; i < mutation.addedNodes.length; i++) {
                            const node = mutation.addedNodes[i];
                            // 检查是否是关键词项
                            if (node.nodeType === 1 && node.classList.contains('keyword-reply-item')) {
                                shouldUpdate = true;
                                break;
                            }
                        }
                    }

                    // 当属性发生变化时（如tempState变更）
                    if (mutation.type === 'attributes' &&
                        mutation.target.classList.contains('keyword-reply-item') &&
                        mutation.attributeName === 'data-temp-state') {
                        shouldUpdate = true;
                    }
                });

                if (shouldUpdate) {
                    // 实时更新右侧关键词列表，添加错误处理
                    try {
                        if (typeof updateKeywordCountAndList === 'function') {
                            updateKeywordCountAndList();
                        } else {
                            console.warn('updateKeywordCountAndList函数未定义');
                        }
                    } catch (error) {
                        console.error('更新关键词列表出错:', error);
                    }
                }
            });

            // 配置观察选项
            const config = {
                childList: true, // 观察子节点的变化
                subtree: true,   // 观察所有后代节点
                attributes: true, // 观察属性变化
                attributeFilter: ['data-temp-state', 'data-id'] // 只观察这些属性的变化
            };

            // 获取父容器
            const container = document.querySelector('.keyword-reply-content');
            if (container) {
                // 开始观察
                observer.observe(container, config);
                console.log('已设置关键词变更观察器');
            }
        }

        // 初始化函数
        function initializeKeywordPage() {
            console.log('初始化关键词页面');

            // 修改标题文本
            const countTitle = document.querySelector('.keyword-count-title');
            if (countTitle && countTitle.textContent === '关键词回复列表中的关键词总数') {
                countTitle.textContent = '关键词总数';
            }

            const listTitle = document.querySelector('.keyword-list-title');
            if (listTitle && listTitle.innerHTML.includes('关键词回复列表中的关键词')) {
                listTitle.innerHTML = '<i class="fas fa-list"></i>关键词列表' +
                    (listTitle.querySelector('.category-name') ?
                    listTitle.querySelector('.category-name').outerHTML : '');
            }

            // 设置特殊区域样式
            const specialArea = document.querySelector('.special-area');
            if (specialArea) {
                specialArea.style.overflowY = 'hidden';
            }

            // 监听关键词编辑区域的变化
            setUpKeywordChangeObserver();
        }

        // 页面加载完成后执行初始化
        document.addEventListener('DOMContentLoaded', initializeKeywordPage);

        // 设置特殊区域布局
        function setupSpecialAreaLayout() {
            // 获取特殊区域元素
            const specialArea = document.querySelector('.special-area');
            if (!specialArea) return;

            // 设置特殊区域不滚动
            specialArea.style.overflowY = 'hidden';

            // 获取关键词列表盒子
            const keywordListBox = document.querySelector('.keyword-list-box');
            if (keywordListBox) {
                // 确保关键词列表盒子有正确的高度和样式
                keywordListBox.style.flex = '1';
                keywordListBox.style.display = 'flex';
                keywordListBox.style.flexDirection = 'column';
                keywordListBox.style.maxHeight = '400px';
                keywordListBox.style.minHeight = '250px';
                keywordListBox.style.overflow = 'hidden';

                // 确保列表内部容器存在并且有正确的样式
                let containerElement = keywordListBox.querySelector('.keyword-list-container');
                if (containerElement) {
                    containerElement.style.flex = '1';
                    containerElement.style.overflowY = 'auto';
                    containerElement.style.minHeight = '0';
                }
            }

            // 确保所有特殊区盒子有统一的边距
            const allBoxes = specialArea.querySelectorAll('.special-box, .keyword-count-box, .keyword-list-box');
            allBoxes.forEach(box => {
                box.style.marginBottom = '16px';
            });

            // 最后一个盒子不需要底部边距
            if (allBoxes.length > 0) {
                allBoxes[allBoxes.length - 1].style.marginBottom = '0';
            }
        }

        // 页面加载完成后执行初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeKeywordPage();
            applySpecialAreaStyles();
        });

        // 直接应用特殊区域样式
        function applySpecialAreaStyles() {
            console.log('应用特殊区域样式');

            // 特殊区域整体样式
            const specialArea = document.querySelector('.special-area');
            if (specialArea) {
                specialArea.style.padding = '12px';
                specialArea.style.overflow = 'hidden';
                // 增加空间利用率，去除多余的空间
                specialArea.style.gap = '0';
            }

            // 获取所有盒子，确保间距为12px
            const boxes = document.querySelectorAll('.special-box, .keyword-count-box, .keyword-list-box');
            boxes.forEach((box, index) => {
                // 所有盒子都使用统一的内边距
                box.style.padding = '12px';

                // 除了最后一个盒子外，其他都有底部边距
                if (index < boxes.length - 1) {
                    box.style.marginBottom = '12px'; // 确保是精确的12px
                } else {
                    box.style.marginBottom = '0'; // 最后一个盒子不需要底部边距
                }

                // 移除可能存在的其他影响布局的样式
                box.style.marginTop = '0';
                box.style.marginLeft = '0';
                box.style.marginRight = '0';
            });

            // 关键词统计盒子样式 - 改为一行显示
            const countBox = document.querySelector('.keyword-count-box');
            if (countBox) {
                countBox.style.display = 'flex';
                countBox.style.alignItems = 'center';
                countBox.style.justifyContent = 'space-between';
                countBox.style.height = 'auto'; // 自适应高度，减少垂直空间占用
            }

            // 关键词统计标题样式
            const countTitle = document.querySelector('.keyword-count-title');
            if (countTitle) {
                countTitle.style.marginBottom = '0';
                countTitle.style.marginRight = '10px';
            }

            // 关键词列表盒子样式 - 增加高度，占用更多空间
            const listBox = document.querySelector('.keyword-list-box');
            if (listBox) {
                listBox.style.flex = '1';
                listBox.style.minHeight = '0';
                listBox.style.marginBottom = '0';
                listBox.style.display = 'flex';
                listBox.style.flexDirection = 'column';
                listBox.style.overflow = 'hidden'; // 确保内容不溢出
            }

            // 关键词列表标题样式 - 调整左边距和图标间距
            const listTitle = document.querySelector('.keyword-list-title');
            if (listTitle) {
                listTitle.style.padding = '12px';
                listTitle.style.paddingLeft = '0'; // 左边距设为0
                listTitle.style.flexShrink = '0';

                // 调整图标和文字之间的间距
                const icon = listTitle.querySelector('i');
                if (icon) {
                    icon.style.marginRight = '8px'; // 增加图标和文字之间的间距
                }
            }

            // 关键词列表容器样式 - 增加滚动区域
            const listContainer = document.querySelector('.keyword-list-container');
            if (listContainer) {
                listContainer.style.flex = '1';
                listContainer.style.overflowY = 'auto';
                listContainer.style.padding = '12px';
                listContainer.style.paddingTop = '6px'; // 减少顶部内边距，增加可视空间
                listContainer.style.minHeight = '0';
            }

            console.log('特殊区域样式应用完成');
        }

        // 更新关键词总数和列表的全局函数
        function updateKeywordCountAndList() {
            console.log('调用全局updateKeywordCountAndList函数');

            const countElement = document.querySelector('.keyword-count-value');
            const keywordListBox = document.querySelector('.keyword-list-box');
            const titleElement = document.querySelector('.keyword-list-title');

            if (!countElement || !keywordListBox || !titleElement) {
                console.warn('找不到关键词列表相关DOM元素');
                return;
            }

            // 计算所有分类组的关键词总数
            let totalKeywords = 0;
            if (typeof window.keywordData === 'object' && window.keywordData !== null) {
                Object.values(window.keywordData).forEach(categoryKeywords => {
                    if (Array.isArray(categoryKeywords)) {
                        totalKeywords += categoryKeywords.length;
                    }
                });
            }
            // 更新总数显示
            countElement.textContent = `${totalKeywords}个`;

            // 更新标题显示的分类名称
            const categoryNameElem = titleElement.querySelector('.category-name');
            if (categoryNameElem) {
                if (window.activeCategory === null) {
                    categoryNameElem.textContent = '（未选中分类组）';
                } else {
                    const categoryName = window.keywordCategories.find(cat => cat.id === window.activeCategory)?.name || '';
                    categoryNameElem.textContent = `（${categoryName}）`;
                }
            }

            // 清除现有项，但保留标题
            const keywordListContainer = keywordListBox.querySelector('.keyword-list-container');
            if (keywordListContainer) {
                keywordListContainer.innerHTML = '';
            } else {
                // 如果不存在容器，则清除除标题外的所有元素
                while (keywordListBox.lastChild && keywordListBox.lastChild !== titleElement) {
                    keywordListBox.removeChild(keywordListBox.lastChild);
                }

                // 创建滚动容器
                const newContainer = document.createElement('div');
                newContainer.className = 'keyword-list-container';
                newContainer.style.maxHeight = '300px';
                newContainer.style.overflowY = 'auto';
                newContainer.style.backgroundColor = '#f9f9f9';
                newContainer.style.padding = '10px';
                newContainer.style.borderRadius = '6px';
                keywordListBox.appendChild(newContainer);
            }

            // 获取滚动容器引用
            const containerElement = keywordListBox.querySelector('.keyword-list-container');

            // 如果没有选中分类，显示提示信息
            if (window.activeCategory === null) {
                const noSelectionElement = document.createElement('div');
                noSelectionElement.style.padding = '10px';
                noSelectionElement.style.color = '#666';
                noSelectionElement.textContent = '请先选择一个关键词分类组';
                containerElement.appendChild(noSelectionElement);
                return;
            }

            // 获取当前分类的关键词
            const categoryKeywords = window.keywordData[window.activeCategory] || [];

            // 显示当前分类的关键词列表
            if (categoryKeywords.length === 0) {
                const emptyElement = document.createElement('div');
                emptyElement.style.padding = '10px';
                emptyElement.style.color = '#666';
                emptyElement.textContent = '当前分类下暂无关键词';
                containerElement.appendChild(emptyElement);
            } else {
                categoryKeywords.forEach(kw => {
                    const keywordItem = document.createElement('div');
                    keywordItem.className = 'keyword-list-item';
                    keywordItem.textContent = kw.keyword || '(空)';
                    containerElement.appendChild(keywordItem);
                });
            }
        }

        // 用于跟踪已显示的日志，避免重复
        window.displayedLogIds = window.displayedLogIds || new Set();

        // 生成日志的唯一ID
        function generateLogId(log) {
            // 使用时间戳、实例ID和消息内容的组合作为唯一标识
            return `${log.timestamp}_${log.instanceId}_${log.message}`;
        }

        // 更新日志表格显示
        function updateLogsDisplay(newLog = null) {
            const logTableBody = document.querySelector('.log-table tbody');
            if (!logTableBody) return;

            // 如果提供了新日志，只添加这一条
            if (newLog) {
                // 如果当前有实例过滤，且不是该实例的日志，则不显示
                if (window.currentLogInstanceId && newLog.instanceId !== window.currentLogInstanceId) {
                    return;
                }

                // 过滤掉包含"抖店飞鸽AI自动聊天监控"的日志
                if (newLog.message && newLog.message.includes('抖店飞鸽AI自动聊天监控')) {
                    console.log('跳过轮询开始日志:', newLog.message);
                    return;
                }

                // 生成日志的唯一ID
                const logId = generateLogId(newLog);

                // 检查是否已经显示过这条日志
                if (window.displayedLogIds.has(logId)) {
                    console.log('跳过重复日志:', newLog.message);
                    return;
                }

                // 记录这条日志已经显示
                window.displayedLogIds.add(logId);

                const logRow = createLogTableRow(newLog);
                logTableBody.insertBefore(logRow, logTableBody.firstChild); // 插入到顶部

                // 不限制表格中显示的条目数，显示所有日志
                // 注释掉原有的限制代码
                // const maxDisplayRows = 1000;
                // while (logTableBody.children.length > maxDisplayRows) {
                //     logTableBody.removeChild(logTableBody.lastChild);
                // }

                // 保存日志到本地存储
                if (typeof categoryStorageAdapter !== 'undefined') {
                    categoryStorageAdapter.addLog(newLog);
                }

                // 检查是否包含用户信息，更新用户列表
                const userMatch = newLog.message && newLog.message.match(/【(.+?)】聊天记录：/);
                if (userMatch && userMatch[1]) {
                    updateMonitoredUsersDisplay();
                }

                return;
            }

            // 否则重新生成整个日志表格
            logTableBody.innerHTML = '';

            // 清空已显示日志的记录
            if (window.displayedLogIds) {
                window.displayedLogIds.clear();
            }

            // 显示所有日志，不限制数量，按时间倒序排列
            let recentLogs = [...window.logs]
                .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // 过滤掉包含"抖店飞鸽AI自动聊天监控"的日志
            recentLogs = recentLogs.filter(log => {
                return !(log.message && log.message.includes('抖店飞鸽AI自动聊天监控'));
            });

            // 如果当前有实例过滤，只显示该实例的日志
            if (window.currentLogInstanceId) {
                recentLogs = recentLogs.filter(log => log.instanceId === window.currentLogInstanceId);
            }

            recentLogs.forEach(log => {
                // 生成日志的唯一ID并记录
                const logId = generateLogId(log);
                window.displayedLogIds.add(logId);

                const logRow = createLogTableRow(log);
                logTableBody.appendChild(logRow);
            });

            // 更新用户列表显示
            updateMonitoredUsersDisplay();
        }

        // 更新监控用户列表显示
        function updateMonitoredUsersDisplay() {
            const usersContainer = document.getElementById('monitoredUsersScroll');
            if (!usersContainer) return;

            // 清空现有用户列表
            usersContainer.innerHTML = '';

            // 获取所有用户
            let allUsers = [];

            // 如果当前有实例过滤，只显示该实例的用户
            if (window.currentLogInstanceId && window.monitoredUsers[window.currentLogInstanceId]) {
                // 直接使用实例的用户列表，保持原有顺序（新用户在前）
                // 创建一个新数组，避免引用原数组
                allUsers = [...window.monitoredUsers[window.currentLogInstanceId]];
            } else {
                // 合并所有实例的用户，保持顺序并去重
                // 使用数组而不是Set来保持顺序
                const userArray = [];
                const userSet = new Set(); // 仅用于去重检查

                // 遍历所有实例的用户列表
                // 输出monitoredUsers对象，用于调试
                console.log("monitoredUsers对象:", JSON.stringify(window.monitoredUsers));

                // 使用Object.entries而不是Object.values，以便我们可以看到键
                Object.entries(window.monitoredUsers).forEach(([instanceId, users]) => {
                    console.log(`实例 ${instanceId} 的用户列表:`, users);
                    if (Array.isArray(users)) {
                        // 对每个实例的用户列表，按顺序添加到总列表中
                        users.forEach(user => {
                            // 如果用户尚未添加，则添加到列表中
                            if (!userSet.has(user)) {
                                userArray.push(user);
                                userSet.add(user);
                                console.log(`添加用户 ${user} 到合并列表`);
                            }
                        });
                    }
                });

                allUsers = userArray;
            }

            // 检查日志中是否有未添加到用户列表的用户（只在没有实例过滤时执行）
            if (!window.currentLogInstanceId && window.logs && window.logs.length > 0) {
                // 创建一个新的数组来存储从日志中提取的用户
                const newUsersFromLogs = [];

                window.logs.forEach(log => {
                    if (log.message) {
                        // 统一使用【】符号内的内容作为用户名
                        const match = log.message.match(/【(.+?)】聊天记录：/);
                        if (match && match[1]) {
                            const username = match[1];
                            if (!allUsers.includes(username) && !newUsersFromLogs.includes(username)) {
                                console.log(`从日志中提取到新用户: ${username}`);
                                newUsersFromLogs.unshift(username); // 添加到新用户列表的首位
                            }
                        }
                    }
                });

                // 将新用户添加到用户列表的前面
                allUsers = [...newUsersFromLogs, ...allUsers];
            }

            // 完全按照监控顺序排序，不进行任何基于用户名的排序
            // 最新监控到的用户排在最前面
            console.log("用户列表排序:", allUsers);

            // 不进行任何特殊排序，保持原始监控顺序
            // 这确保了用户列表按照监控顺序排序，最新监控到的用户排在最前面

            // 如果没有用户，显示提示信息
            if (allUsers.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-users-message';
                emptyMessage.textContent = '暂无监控到的用户';
                usersContainer.appendChild(emptyMessage);
                return;
            }

            // 添加用户项 - 按照原始顺序添加，不进行任何排序
            // 这确保了用户列表按照监控顺序排序，最新监控到的用户排在最前面
            console.log("最终用户列表顺序:", allUsers);

            allUsers.forEach(username => {
                const userItem = document.createElement('div');
                userItem.className = 'monitored-user-item';
                userItem.innerHTML = `<i class="fas fa-user"></i>【${username}】`;

                // 添加调试信息，显示用户在列表中的位置
                console.log(`添加用户项: ${username}, 位置: ${allUsers.indexOf(username)}`);

                // 点击用户项时，过滤显示该用户的日志
                userItem.addEventListener('click', () => {
                    // 实现用户日志过滤功能
                    filterLogsByUser(username);
                });

                usersContainer.appendChild(userItem);
            });
        }

        // 获取第一个监控用户
        function getFirstMonitoredUser() {
            if (!window.monitoredUsers) return null;

            // 获取当前实例ID
            const instanceId = window.currentLogInstanceId || '1'; // 默认使用实例1

            // 获取当前实例的用户列表
            const users = window.monitoredUsers[instanceId] || [];

            // 返回第一个用户，如果没有则返回null
            return users.length > 0 ? users[0] : null;
        }

        // 根据用户名过滤日志
        function filterLogsByUser(username) {
            const logTableBody = document.querySelector('.log-table tbody');
            if (!logTableBody) return;

            // 如果当前已经在显示该用户的日志，则不重复加载
            let runningLogBtn = document.getElementById('runningLogBtn');
            if (window.currentFilteredUsername === username &&
                runningLogBtn &&
                runningLogBtn.innerText.trim().includes(`用户【${username}】的日志`)) {
                console.log(`已经在显示用户 ${username} 的日志，不重复加载`);
                return;
            }

            // 保存当前筛选的用户名
            window.currentFilteredUsername = username;

            // 重置查看所有日志的标记
            window.viewingAllLogs = false;

            // 清空现有日志
            logTableBody.innerHTML = '';

            // 创建加载指示器
            const loadingRow = document.createElement('tr');
            const loadingCell = document.createElement('td');
            loadingCell.colSpan = 3;
            loadingCell.textContent = `正在加载用户 ${username} 的日志...`;
            loadingCell.style.textAlign = 'center';
            loadingCell.style.padding = '20px';
            loadingRow.appendChild(loadingCell);
            logTableBody.appendChild(loadingRow);

            // 获取实例ID：优先使用当前实例ID，否则从用户所属实例获取，最后默认为1
            let instanceId = window.currentLogInstanceId;
            if (!instanceId) {
                // 如果没有当前实例ID，尝试从监控用户列表中找到该用户所属的实例
                for (const [id, users] of Object.entries(window.monitoredUsers || {})) {
                    if (users && users.includes(username)) {
                        instanceId = id;
                        break;
                    }
                }
                // 如果还是没找到，默认为实例1
                if (!instanceId) {
                    instanceId = '1';
                }
            }

            // 更新按钮文本，先显示加载状态
            runningLogBtn = document.getElementById('runningLogBtn');
            if (runningLogBtn) {
                runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>正在加载...`;
                // 清除特殊标记
                delete runningLogBtn.dataset.viewMode;
            }

            // 从本地存储加载用户日志文件
            loadUserLogFile(instanceId, username)
                .then(userLogData => {
                    if (userLogData && userLogData.pollingId) {
                        // 如果找到用户日志文件并且包含轮询ID，加载对应的轮询日志
                        return loadPollingLogFile(instanceId, userLogData.pollingId);
                    } else {
                        // 如果没有找到用户日志文件或者没有轮询ID，使用旧的过滤方式
                        console.log(`未找到用户 ${username} 的日志文件或轮询ID，使用旧的过滤方式`);
                        return fallbackFilterLogsByUser(username);
                    }
                })
                .then(logs => {
                    // 清空加载指示器
                    logTableBody.innerHTML = '';

                    if (Array.isArray(logs) && logs.length > 0) {
                        // 显示轮询的所有日志，不再按用户名筛选
                        // 因为该轮询的所有日志都属于该用户的最新轮询
                        const allLogs = [...logs].reverse(); // 按时间倒序排列

                        console.log(`显示用户 ${username} 的最新轮询日志，共 ${allLogs.length} 条`);

                        // 添加所有日志
                        allLogs.forEach(log => {
                            const logRow = createLogTableRow(log);
                            logTableBody.appendChild(logRow);
                        });
                    } else {
                        // 显示无日志提示
                        const noLogRow = document.createElement('tr');
                        const noLogCell = document.createElement('td');
                        noLogCell.colSpan = 3;
                        noLogCell.textContent = `没有找到用户 ${username} 的日志`;
                        noLogCell.style.textAlign = 'center';
                        noLogCell.style.padding = '20px';
                        noLogRow.appendChild(noLogCell);
                        logTableBody.appendChild(noLogRow);

                        console.log(`没有找到用户 ${username} 的日志`);
                    }

                    // 更新标题，显示当前过滤状态（始终添加实例前缀）
                    if (runningLogBtn) {
                        runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户【${username}】的日志`;
                        // 清除特殊标记
                        delete runningLogBtn.dataset.viewMode;
                    }
                })
                .catch(error => {
                    console.error(`加载用户 ${username} 的日志失败:`, error);

                    // 清空加载指示器
                    logTableBody.innerHTML = '';

                    // 显示错误提示
                    const errorRow = document.createElement('tr');
                    const errorCell = document.createElement('td');
                    errorCell.colSpan = 3;
                    errorCell.textContent = `加载用户 ${username} 的日志失败`;
                    errorCell.style.textAlign = 'center';
                    errorCell.style.padding = '20px';
                    errorCell.style.color = 'red';
                    errorRow.appendChild(errorCell);
                    logTableBody.appendChild(errorRow);

                    // 更新按钮文本（始终添加实例前缀）
                    if (runningLogBtn) {
                        runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户【${username}】的日志`;
                        // 清除特殊标记
                        delete runningLogBtn.dataset.viewMode;
                    }

                    // 出错时使用旧的过滤方式
                    fallbackFilterLogsByUser(username);
                });
        }

        // 从本地存储加载用户日志文件
        function loadUserLogFile(instanceId, username) {
            return new Promise((resolve, reject) => {
                // 使用IPC通信加载用户日志文件
                if (window.electronAPI) {
                    // 添加一次性监听器，接收加载结果
                    const handleReply = (result) => {
                        // 移除监听器，避免内存泄漏
                        window.electronAPI.removeListener('load-user-log-file-reply', handleReply);

                        if (result.success) {
                            console.log(`成功加载用户 ${username} 的日志文件，关联轮询ID: ${result.data.pollingId}${result.data.previousPollingId ? '，上一个轮询ID: ' + result.data.previousPollingId : ''}`);
                            resolve(result.data);
                        } else {
                            console.warn(`加载用户 ${username} 的日志文件失败:`, result.error);
                            resolve(null); // 失败时返回null，而不是reject
                        }
                    };

                    // 添加监听器
                    window.electronAPI.onLoadUserLogFileReply(handleReply);

                    // 发送加载请求
                    window.electronAPI.loadUserLogFile(instanceId, username);
                } else {
                    console.error('electronAPI不可用，无法加载用户日志文件');
                    resolve(null);
                }
            });
        }

        // 从本地存储加载轮询日志文件
        function loadPollingLogFile(instanceId, pollingId) {
            return new Promise((resolve, reject) => {
                // 使用IPC通信加载轮询日志文件
                if (window.electronAPI) {
                    // 添加一次性监听器，接收加载结果
                    const handleReply = (result) => {
                        // 移除监听器，避免内存泄漏
                        window.electronAPI.removeListener('load-polling-log-file-reply', handleReply);

                        if (result.success) {
                            console.log(`成功加载轮询 ${pollingId} 的日志文件，包含 ${result.logs.length} 条日志`);
                            resolve(result.logs);
                        } else {
                            console.error(`加载轮询 ${pollingId} 的日志文件失败:`, result.error);
                            resolve([]); // 失败时返回空数组，而不是reject
                        }
                    };

                    // 添加监听器
                    window.electronAPI.onLoadPollingLogFileReply(handleReply);

                    // 发送加载请求
                    window.electronAPI.loadPollingLogFile(instanceId, pollingId);
                } else {
                    console.error('electronAPI不可用，无法加载轮询日志文件');
                    resolve([]);
                }
            });
        }

        // 加载所有监控用户的日志
        function loadAllMonitoredUsersLogs() {
            return new Promise(async (resolve) => {
                // 获取当前实例ID
                const instanceId = window.currentLogInstanceId || '1'; // 默认使用实例1

                // 获取用户列表 - 使用与updateMonitoredUsersDisplay相同的逻辑
                let allUsers = [];
                const userSet = new Set(); // 用于去重检查

                // 首先添加监控用户列表中的用户
                if (window.monitoredUsers[instanceId]) {
                    Array.from(window.monitoredUsers[instanceId]).forEach(user => {
                        if (!userSet.has(user)) {
                            allUsers.push(user);
                            userSet.add(user);
                        }
                    });
                }

                // 然后检查日志中的用户并添加到列表（与log-user-sorter.js中的逻辑保持一致）
                if (window.logs && window.logs.length > 0) {
                    window.logs.forEach(log => {
                        // 只处理当前实例的日志
                        if (log.instanceId === instanceId && log.message) {
                            const match = log.message.match(/【(.+?)】聊天记录：/);
                            if (match && match[1]) {
                                const username = match[1];
                                if (!userSet.has(username)) {
                                    allUsers.push(username);
                                    userSet.add(username);
                                }
                            }
                        }
                    });
                }

                if (allUsers.length === 0) {
                    console.log('没有监控到的用户，无法加载日志');
                    resolve([]);
                    return;
                }

                console.log(`开始加载 ${allUsers.length} 个监控用户的日志`);

                // 存储所有用户的日志
                const allLogs = [];

                // 依次加载每个用户的日志
                for (const username of allUsers) {
                    try {
                        // 加载用户日志文件，获取轮询ID
                        const userLogData = await loadUserLogFile(instanceId, username);

                        if (userLogData && userLogData.pollingId) {
                            // 加载对应的轮询日志
                            const logs = await loadPollingLogFile(instanceId, userLogData.pollingId);

                            if (logs && logs.length > 0) {
                                // 添加用户标识到日志中，方便区分
                                const userLogs = logs.map(log => ({
                                    ...log,
                                    username: username // 添加用户名字段
                                }));

                                // 将该用户的日志添加到总日志中
                                allLogs.push(...userLogs);
                                console.log(`已加载用户 ${username} 的 ${userLogs.length} 条日志`);
                            }
                        }
                    } catch (error) {
                        console.error(`加载用户 ${username} 的日志失败:`, error);
                    }
                }

                // 过滤掉包含"抖店飞鸽AI自动聊天监控"的日志
                const filteredLogs = allLogs.filter(log => {
                    return !(log.message && log.message.includes('抖店飞鸽AI自动聊天监控'));
                });

                // 按时间排序所有日志
                filteredLogs.sort((a, b) => {
                    // 如果有时间戳，按时间戳排序
                    if (a.timestamp && b.timestamp) {
                        return new Date(a.timestamp) - new Date(b.timestamp);
                    }
                    return 0;
                });

                console.log(`成功加载所有监控用户的日志，共 ${filteredLogs.length} 条（已过滤掉轮询开始标记）`);
                resolve(filteredLogs);
            });
        }

        // 显示所有监控用户的日志
        function displayAllMonitoredUsersLogs() {
            const logTableBody = document.querySelector('.log-table tbody');
            if (!logTableBody) return;

            // 清空现有日志
            logTableBody.innerHTML = '';

            // 标记当前是查看所有日志模式
            window.viewingAllLogs = true;

            // 加载所有监控用户的日志
            loadAllMonitoredUsersLogs().then(allLogs => {
                // 如果在加载过程中切换了视图，不再显示这些日志
                if (!window.viewingAllLogs) {
                    console.log('视图已切换，取消显示所有日志');
                    return;
                }

                if (allLogs.length === 0) {
                    console.log('没有找到任何监控用户的日志');
                    return;
                }

                // 再次清空日志表格，确保不会有重复
                logTableBody.innerHTML = '';

                // 显示所有日志
                allLogs.forEach(log => {
                    const logRow = createLogTableRow(log);
                    logTableBody.appendChild(logRow);
                });

                // 更新按钮文本
                const runningLogBtn = document.getElementById('runningLogBtn');
                if (runningLogBtn) {
                    // 如果当前有实例过滤，显示实例名称；否则显示"所有日志"
                    if (window.currentLogInstanceId) {
                        runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${window.currentLogInstanceId}`;
                        // 清除特殊标记
                        delete runningLogBtn.dataset.viewMode;
                    } else {
                        runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>所有日志`;
                        // 添加特殊标记，表示这是"所有日志"按钮
                        runningLogBtn.dataset.viewMode = 'all-logs';
                    }
                }

                console.log(`已显示所有监控用户的日志，共 ${allLogs.length} 条`);
            }).catch(error => {
                console.error('加载所有监控用户的日志失败:', error);
                // 出错时也重置标记
                window.viewingAllLogs = false;
            });
        }

        // 旧的日志过滤方式（备用）
        function fallbackFilterLogsByUser(username) {
            // 过滤包含该用户名的日志
            const userPattern = new RegExp(`【${username}】`);
            const filteredLogs = window.logs.filter(log => {
                return (log.message && userPattern.test(log.message)) ||
                       (log.content && userPattern.test(log.content));
            }).reverse(); // 不限制数量，显示所有匹配的日志

            // 添加过滤后的日志
            const logTableBody = document.querySelector('.log-table tbody');
            if (logTableBody) {
                filteredLogs.forEach(log => {
                    const logRow = createLogTableRow(log);
                    logTableBody.appendChild(logRow);
                });
            }

            // 更新按钮文本（始终添加实例前缀）
            const runningLogBtn = document.getElementById('runningLogBtn');
            if (runningLogBtn) {
                // 获取实例ID：优先使用当前实例ID，否则从用户所属实例获取，最后默认为1
                let instanceId = window.currentLogInstanceId;
                if (!instanceId) {
                    // 如果没有当前实例ID，尝试从监控用户列表中找到该用户所属的实例
                    for (const [id, users] of Object.entries(window.monitoredUsers || {})) {
                        if (users && users.includes(username)) {
                            instanceId = id;
                            break;
                        }
                    }
                    // 如果还是没找到，默认为实例1
                    if (!instanceId) {
                        instanceId = '1';
                    }
                }
                runningLogBtn.innerHTML = `<i class="fas fa-list-alt"></i>实例 ${instanceId} - 用户【${username}】的日志`;
                // 清除特殊标记
                delete runningLogBtn.dataset.viewMode;
            }

            return Promise.resolve(filteredLogs);
        }

        // 初始化日志右键菜单
        function initLogContextMenu() {
            const runningLogBtn = document.getElementById('runningLogBtn');
            const contextMenu = document.getElementById('logContextMenu');
            const instancesMenuList = document.getElementById('instancesMenuList');
            const viewAllLogsMenuItem = document.getElementById('viewAllLogsMenuItem');

            if (!runningLogBtn || !contextMenu || !instancesMenuList || !viewAllLogsMenuItem) return;

            // 右键点击运行日志按钮时显示菜单或取消实例筛选
            runningLogBtn.addEventListener('contextmenu', (e) => {
                e.preventDefault();

                const currentText = runningLogBtn.innerText.trim();

                // 如果当前是实例状态，右键点击取消筛选，回到所有日志状态
                if (currentText === '实例 1' || currentText === '实例 2' || currentText === '实例 3' ||
                    currentText === '实例 4' || currentText === '实例 5' || currentText === '实例 6') {

                    // 重置实例过滤和用户筛选，显示所有实例的所有用户
                    window.currentLogInstanceId = null;
                    window.currentFilteredUsername = null;

                    // 先更新用户列表显示（这样用户列表会显示所有实例的用户）
                    updateMonitoredUsersDisplay();

                    // 显示所有监控用户的日志
                    displayAllMonitoredUsersLogs();

                    return; // 不显示右键菜单
                }

                // 其他情况显示右键菜单
                // 更新实例列表
                updateInstancesMenu();

                // 显示菜单
                contextMenu.style.display = 'block';
                contextMenu.style.left = `${e.pageX}px`;
                contextMenu.style.top = `${e.pageY}px`;
            });

            // 点击查看所有日志菜单项
            viewAllLogsMenuItem.addEventListener('click', () => {
                // 重置实例过滤和用户筛选，显示所有实例的所有用户
                window.currentLogInstanceId = null;
                window.currentFilteredUsername = null;

                // 先更新用户列表显示（这样用户列表会显示所有实例的用户）
                updateMonitoredUsersDisplay();

                // 显示所有监控用户的日志
                displayAllMonitoredUsersLogs();

                // 隐藏菜单
                contextMenu.style.display = 'none';
            });

            // 点击页面其他地方时隐藏菜单
            document.addEventListener('click', (e) => {
                if (!contextMenu.contains(e.target) && e.target !== runningLogBtn) {
                    contextMenu.style.display = 'none';
                }
            });
        }

        // 更新实例菜单
        function updateInstancesMenu() {
            const instancesMenuList = document.getElementById('instancesMenuList');
            if (!instancesMenuList) return;

            // 清空现有实例列表
            instancesMenuList.innerHTML = '';

            // 获取所有实例
            const instances = Object.keys(window.instanceSettings).filter(id => {
                // 只显示已激活的实例
                return window.instanceSettings[id].activated === true;
            });

            // 如果没有实例，显示提示信息
            if (instances.length === 0) {
                const emptyItem = document.createElement('div');
                emptyItem.className = 'context-menu-item';
                emptyItem.textContent = '暂无已打开的实例';
                instancesMenuList.appendChild(emptyItem);
                return;
            }

            // 添加实例项
            instances.forEach(instanceId => {
                const instanceItem = document.createElement('div');
                instanceItem.className = 'context-menu-item instance-item';
                instanceItem.dataset.instanceId = instanceId;
                instanceItem.innerHTML = `<i class="fas fa-desktop"></i>实例 ${instanceId}`;

                // 如果是当前选中的实例，添加active类
                if (window.currentLogInstanceId === instanceId) {
                    instanceItem.classList.add('active');
                }

                // 点击实例项时，过滤显示该实例的日志
                instanceItem.addEventListener('click', () => {
                    // 设置当前实例ID
                    window.currentLogInstanceId = instanceId;

                    // 重置用户筛选
                    window.currentFilteredUsername = null;

                    // 重置查看所有日志的标记
                    window.viewingAllLogs = false;

                    // 先更新用户列表显示（这样用户列表会只显示该实例的用户）
                    updateMonitoredUsersDisplay();

                    // 显示该实例所有用户的最新轮询日志（与"查看所有日志"使用相同的格式）
                    displayAllMonitoredUsersLogs();

                    // 隐藏菜单
                    document.getElementById('logContextMenu').style.display = 'none';
                });

                instancesMenuList.appendChild(instanceItem);
            });
        }

        // 创建日志表格行
        function createLogTableRow(log) {
            const row = document.createElement('tr');

            // 时间列
            const timeCell = document.createElement('td');
            // 如果是ISO格式时间戳，转换为本地时间格式
            let displayTime = log.timestamp;
            if (typeof log.timestamp === 'string' && log.timestamp.includes('T')) {
                try {
                    displayTime = new Date(log.timestamp).toLocaleString();
                } catch (e) {
                    console.error('时间转换错误:', e);
                }
            }
            timeCell.textContent = displayTime;
            row.appendChild(timeCell);

            // 类型列
            const typeCell = document.createElement('td');
            const typeSpan = document.createElement('span');

            // 根据日志类型设置不同的样式和文本
            const icon = document.createElement('i');

            if (log.logType === 'staff') {
                // 客服消息 - 绿色
                typeSpan.className = 'log-type staff';
                typeSpan.style.backgroundColor = '#e6f7e6';
                typeSpan.style.color = '#28a745';
                icon.className = 'fas fa-headset';
                typeSpan.textContent = '客服';
            } else if (log.logType === 'user') {
                // 用户消息 - 红色
                typeSpan.className = 'log-type user';
                typeSpan.style.backgroundColor = '#ffebeb';
                typeSpan.style.color = '#dc3545';
                icon.className = 'fas fa-user';
                typeSpan.textContent = '用户';
            } else {
                // 系统消息 - 蓝色
                typeSpan.className = 'log-type system';
                typeSpan.style.backgroundColor = '#e8f4fd';
                typeSpan.style.color = '#0c71c3';
                icon.className = 'fas fa-cog';
                typeSpan.textContent = '系统';
            }

            typeSpan.insertBefore(icon, typeSpan.firstChild);
            typeCell.appendChild(typeSpan);
            row.appendChild(typeCell);

            // 内容列
            const contentCell = document.createElement('td');
            let content = log.message || '';

            // 移除控制台格式化标记 "%c"
            content = content.replace(/%c/g, '');

            // 如果是AI监控日志，添加实例ID信息
            if (log.instanceId && content.includes('[AI监控]')) {
                const instanceBadge = document.createElement('span');
                instanceBadge.className = 'instance-badge';
                instanceBadge.textContent = `实例${log.instanceId}`;
                instanceBadge.style.marginRight = '5px';
                instanceBadge.style.padding = '2px 6px';
                instanceBadge.style.backgroundColor = '#eef1ff';
                instanceBadge.style.color = '#4a6bdf';
                instanceBadge.style.borderRadius = '4px';
                instanceBadge.style.fontSize = '11px';

                const cleanContent = content.replace('[AI监控]', '').trim();
                contentCell.textContent = cleanContent;
                contentCell.insertBefore(instanceBadge, contentCell.firstChild);
            } else {
                contentCell.textContent = content;
            }

            row.appendChild(contentCell);

            return row;
        }