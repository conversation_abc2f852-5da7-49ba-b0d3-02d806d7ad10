/**
 * 日志用户排序扩展
 * 
 * 这个脚本替换了原有的updateMonitoredUsersDisplay函数，
 * 添加了根据不同排序方式对用户列表进行排序的功能
 */

// 存储用户首次出现的顺序
window.userDiscoveryOrder = window.userDiscoveryOrder || [];

// 在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('日志用户排序扩展已加载');
    
    // 日志区用户排序选择器
    const logUserSortSelector = document.getElementById('logUserSortMethod');
    if (logUserSortSelector) {
        // 设置默认排序方法为按监控顺序倒序
        let defaultSortMethod = 'reverseMonitorOrder';
        
        // 加载保存的排序方法
        if (window.electronAPI && typeof window.electronAPI.getSetting === 'function') {
            window.electronAPI.getSetting('logUserSortMethod').then(method => {
                if (method) {
                    logUserSortSelector.value = method;
                    console.log('已加载排序方法:', method);
                } else {
                    // 如果没有保存过排序方法，设置为默认值
                    logUserSortSelector.value = defaultSortMethod;
                    
                    // 保存默认排序方法
                    if (window.electronAPI && typeof window.electronAPI.updateSetting === 'function') {
                        window.electronAPI.updateSetting('logUserSortMethod', defaultSortMethod);
                    }
                    
                    console.log('已设置默认排序方法:', defaultSortMethod);
                }
            }).catch(err => {
                console.error('加载日志用户排序方法失败:', err);
                // 设置为默认值
                logUserSortSelector.value = defaultSortMethod;
            });
        } else {
            // 如果没有electronAPI，直接设置为默认值
            logUserSortSelector.value = defaultSortMethod;
        }
        
        // 监听排序方法变更
        logUserSortSelector.addEventListener('change', function() {
            const method = this.value;
            console.log('日志用户排序方法已更改为:', method);
            
            // 保存选择的排序方法
            if (window.electronAPI && typeof window.electronAPI.updateSetting === 'function') {
                window.electronAPI.updateSetting('logUserSortMethod', method);
                window.electronAPI.onUpdateSettingReply((result) => {
                    if (result.success) {
                        console.log('日志用户排序方法已保存');
                    } else {
                        console.error('保存日志用户排序方法失败:', result.error);
                    }
                });
            }
            
            // 重新显示用户列表
            if (typeof window.updateMonitoredUsersDisplay === 'function') {
                window.updateMonitoredUsersDisplay();
            }
        });
    }
    
    // 保存原始函数的引用
    const originalUpdateMonitoredUsersDisplay = window.updateMonitoredUsersDisplay;
    
    // 如果原始函数存在，替换它
    if (typeof originalUpdateMonitoredUsersDisplay === 'function') {
        // 重新定义函数，添加排序功能
        window.updateMonitoredUsersDisplay = function() {
            const usersContainer = document.getElementById('monitoredUsersScroll');
            if (!usersContainer) return;
    
            // 清空现有用户列表
            usersContainer.innerHTML = '';
    
            // 获取所有用户，同时记录用户首次出现的顺序
            let allUsers = [];
            const userSet = new Set(); // 用于去重检查
    
            // 如果当前有实例过滤，只显示该实例的用户
            if (window.currentLogInstanceId && window.monitoredUsers[window.currentLogInstanceId]) {
                // 添加监控用户列表中的用户
                Array.from(window.monitoredUsers[window.currentLogInstanceId]).forEach(user => {
                    if (!userSet.has(user)) {
                        allUsers.push(user);
                        userSet.add(user);
                        
                        // 记录用户首次出现顺序
                        if (!window.userDiscoveryOrder.includes(user)) {
                            window.userDiscoveryOrder.push(user);
                        }
                    }
                });
            } else {
                // 合并所有实例的用户
                Object.entries(window.monitoredUsers).forEach(([instanceId, users]) => {
                    if (Array.isArray(users)) {
                        users.forEach(user => {
                            if (!userSet.has(user)) {
                                allUsers.push(user);
                                userSet.add(user);
                                
                                // 记录用户首次出现顺序
                                if (!window.userDiscoveryOrder.includes(user)) {
                                    window.userDiscoveryOrder.push(user);
                                }
                            }
                        });
                    }
                });
            }

            // 检查日志中的用户并添加到列表（只在没有实例过滤时执行）
            if (!window.currentLogInstanceId && window.logs && window.logs.length > 0) {
                window.logs.forEach(log => {
                    if (log.message) {
                        const match = log.message.match(/【(.+?)】聊天记录：/);
                        if (match && match[1]) {
                            const username = match[1];
                            if (!userSet.has(username)) {
                                allUsers.push(username);
                                userSet.add(username);

                                // 记录用户首次出现顺序
                                if (!window.userDiscoveryOrder.includes(username)) {
                                    window.userDiscoveryOrder.push(username);
                                }
                            }
                        }
                    }
                });
            }

            console.log("排序前用户列表:", allUsers);
            console.log("用户发现顺序:", window.userDiscoveryOrder);
            
            // 获取当前选择的排序方法
            let sortMethod = "reverseMonitorOrder"; // 默认排序方法改为倒序
            
            // 尝试从下拉菜单获取排序方法
            const logUserSortSelector = document.getElementById('logUserSortMethod');
            if (logUserSortSelector) {
                sortMethod = logUserSortSelector.value;
            }
            
            console.log("当前排序方法:", sortMethod);
            
            // 根据排序方法对用户列表进行排序
            switch (sortMethod) {
                case "reverseMonitorOrder":
                    // 监控顺序倒序
                    allUsers.sort((a, b) => {
                        const indexA = window.userDiscoveryOrder.indexOf(a);
                        const indexB = window.userDiscoveryOrder.indexOf(b);
                        return indexB - indexA; // 倒序排列
                    });
                    console.log("按监控顺序倒序排序");
                    break;
                    
                case "alphabetical":
                    // 按用户名字母顺序
                    allUsers.sort((a, b) => a.localeCompare(b));
                    console.log("按用户名字母顺序排序");
                    break;
                    
                case "reverseAlphabetical":
                    // 按用户名字母倒序
                    allUsers.sort((a, b) => b.localeCompare(a));
                    console.log("按用户名字母倒序排序");
                    break;
                    
                case "monitorOrder":
                default:
                    // 原始监控顺序（基于用户首次出现的顺序）
                    allUsers.sort((a, b) => {
                        const indexA = window.userDiscoveryOrder.indexOf(a);
                        const indexB = window.userDiscoveryOrder.indexOf(b);
                        return indexA - indexB;
                    });
                    console.log("按原始监控顺序排序");
                    break;
            }
    
            // 如果没有用户，显示提示信息
            if (allUsers.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-users-message';
                emptyMessage.textContent = '暂无监控到的用户';
                usersContainer.appendChild(emptyMessage);
                return;
            }
    
            // 添加排序后的用户列表
            console.log("排序后用户列表顺序:", allUsers);
    
            allUsers.forEach(username => {
                const userItem = document.createElement('div');
                userItem.className = 'monitored-user-item';
                userItem.innerHTML = `<i class="fas fa-user"></i>【${username}】`;
    
                // 点击用户项时，过滤显示该用户的日志
                userItem.addEventListener('click', () => {
                    window.filterLogsByUser(username);
                });
    
                usersContainer.appendChild(userItem);
            });
        };
        
        console.log('日志用户排序函数已替换');
    }
}); 